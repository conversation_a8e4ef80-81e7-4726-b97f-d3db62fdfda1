import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  final Dio _dio = Dio(
    BaseOptions(
      baseUrl: "http://172.20.10.4:3000", // ✅ Use correct backend URL
      connectTimeout: Duration(seconds: 15),
      receiveTimeout: Duration(seconds: 60),
      headers: {"Content-Type": "application/json"},
    ),
  );

  // ✅ Get Token from SharedPreferences
  Future<String?> getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString("token");
  }

  // ✅ Function to add authorization headers if required
  Future<Map<String, dynamic>> _buildHeaders(
      {bool requiresAuth = false}) async {
    Map<String, dynamic> headers = {"Content-Type": "application/json"};
    if (requiresAuth) {
      String? token = await getToken();
      if (token != null) {
        headers["Authorization"] = "Bearer $token";
      }
    }
    return headers;
  }

  // ✅ POST Request
  Future<Response> post(String endpoint, dynamic data,
      {bool requiresAuth = false}) async {
    try {
      return await _dio.post(
        endpoint,
        data: data,
        options:
            Options(headers: await _buildHeaders(requiresAuth: requiresAuth)),
      );
    } catch (e) {
      throw Exception("Error in POST request: ${e.toString()}");
    }
  }

  // ✅ GET Request (Fixed: Supports Query Parameters)
  Future<Response> get(String endpoint,
      {bool requiresAuth = false, Map<String, dynamic>? queryParams}) async {
    try {
      return await _dio.get(
        endpoint,
        queryParameters: queryParams, // ✅ Added query parameters support
        options:
            Options(headers: await _buildHeaders(requiresAuth: requiresAuth)),
      );
    } catch (e) {
      throw Exception("Error in GET request: ${e.toString()}");
    }
  }

  // ✅ PUT Request (For Updating Profile)
  Future<Response> put(String endpoint, dynamic data,
      {bool requiresAuth = false}) async {
    try {
      return await _dio.put(
        endpoint,
        data: data,
        options:
            Options(headers: await _buildHeaders(requiresAuth: requiresAuth)),
      );
    } catch (e) {
      throw Exception("Error in PUT request: ${e.toString()}");
    }
  }

  // ✅ DELETE Request
  Future<Response> delete(String endpoint, {bool requiresAuth = false}) async {
    try {
      return await _dio.delete(
        endpoint,
        options:
            Options(headers: await _buildHeaders(requiresAuth: requiresAuth)),
      );
    } catch (e) {
      throw Exception("Error in DELETE request: ${e.toString()}");
    }
  }

  // ✅ PATCH Request (For Updating Resources)
  Future<Response> patch(String endpoint, dynamic data,
      {bool requiresAuth = false}) async {
    try {
      return await _dio.patch(
        endpoint,
        data: data,
        options:
            Options(headers: await _buildHeaders(requiresAuth: requiresAuth)),
      );
    } catch (e) {
      throw Exception("Error in PATCH request: ${e.toString()}");
    }
  }
}
