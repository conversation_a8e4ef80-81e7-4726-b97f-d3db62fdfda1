abstract class AuthState {}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthAuthenticated extends AuthState {}

class AuthError extends AuthState {
  final String message;
  AuthError(this.message);
}

class ForgotPasswordSuccess extends AuthState {}

class VerifyCodeSuccess extends AuthState {}

class ResetPasswordSuccess extends AuthState {}

class GoogleLoginSuccess extends AuthState {
  final String code;
  final String email;

  GoogleLoginSuccess({required this.code, required this.email});
}

class AuthPasswordResetRequired extends AuthState {}

class RequiresPasswordReset extends AuthState {} // ✅ New state added
