import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/authentication/data/repositories/auth_repository.dart';
import 'package:patiant_app/features/notifications/services/firebase_messaging_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository authRepository;

  AuthBloc(this.authRepository) : super(AuthInitial()) {
    on<LoginEvent>(_onLogin);
    on<LogoutEvent>(_onLogout);
    on<ForgotPasswordEvent>(_onForgotPassword);
    on<VerifyResetCodeEvent>(_onVerifyResetCode);
    on<ResetPasswordEvent>(_onResetPassword);
    on<GoogleLoginEvent>(_onGoogleLogin);
    on<VerifyTwoFactorEvent>(_onVerifyTwoFactor);
    on<ResetLoginPasswordEvent>(_onResetLoginPassword); // ✅ Register here
  }

  Future<void> _onLogin(LoginEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      bool shouldRedirectToHome =
          await authRepository.login(event.email, event.password);

      if (shouldRedirectToHome) {
        // Initialize FCM token after successful login
        await _initializeFCMToken();
        emit(AuthAuthenticated());
      } else {
        emit(RequiresPasswordReset()); // Redirect user to reset password screen
      }
    } catch (e) {
      emit(AuthError("Login failed"));
    }
  }

  Future<void> _onLogout(LogoutEvent event, Emitter<AuthState> emit) async {
    await authRepository.logout();
    emit(AuthInitial());
  }

  Future<void> _onForgotPassword(
      ForgotPasswordEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      await authRepository.forgotPassword(event.email);
      emit(ForgotPasswordSuccess());
    } catch (e) {
      emit(AuthError("Failed to send reset email"));
    }
  }

  Future<void> _onVerifyResetCode(
      VerifyResetCodeEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      await authRepository.verifyResetCode(event.email, event.code);
      emit(VerifyCodeSuccess());
    } catch (e) {
      emit(AuthError("Invalid or expired reset code"));
    }
  }

  Future<void> _onResetPassword(
      ResetPasswordEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      await authRepository.resetPassword(
          event.email, event.code, event.newPassword, event.confirmNewPassword);
      emit(ResetPasswordSuccess());
    } catch (e) {
      emit(AuthError("Failed to reset password"));
    }
  }

  Future<void> _onGoogleLogin(
      GoogleLoginEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      final result = await authRepository.initiateGoogleLogin();

      if (result != null) {
        final parts = result.split(':');
        final email = parts[0];
        final code = parts[1];

        // Emit success state with email and code
        emit(GoogleLoginSuccess(code: code, email: email));
      } else {
        emit(AuthError("Google login failed"));
      }
    } catch (e) {
      emit(AuthError("Google login failed: ${e.toString()}"));
    }
  }

  // 🛠️ Handle VerifyTwoFactorEvent
  Future<void> _onVerifyTwoFactor(
      VerifyTwoFactorEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      final token =
          await authRepository.verifyTwoFactor(event.email, event.code);

      if (token != null) {
        // Initialize FCM token after successful 2FA verification
        await _initializeFCMToken();
        emit(AuthAuthenticated()); // Success → Proceed to Home Screen
      } else {
        emit(AuthError("Invalid 2FA code"));
      }
    } catch (e) {
      emit(AuthError("2FA verification failed"));
    }
  }

  Future<void> _onResetLoginPassword(
      ResetLoginPasswordEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      final success = await authRepository.resetLoginPassword(
        event.newPassword,
        event.confirmPassword,
      );

      if (success) {
        // Initialize FCM token after successful password reset
        await _initializeFCMToken();
        emit(AuthAuthenticated()); // Redirect user to HomeScreen after success
      } else {
        emit(AuthError("Failed to reset password"));
      }
    } catch (e) {
      emit(AuthError("Error resetting password: ${e.toString()}"));
    }
  }

  // Initialize FCM token and send to backend
  Future<void> _initializeFCMToken() async {
    try {
      // Get FCM token
      final token = await FirebaseMessagingService.getToken();
      if (token != null) {
        print('FCM Token obtained: $token');

        // Send token to backend
        await authRepository.sendFCMToken(token);
        print('FCM Token sent to backend successfully');
      } else {
        print('Failed to obtain FCM token');
      }
    } catch (e) {
      print('Error initializing FCM token: $e');
      // Don't throw error - continue with login even if FCM fails
    }
  }
}
