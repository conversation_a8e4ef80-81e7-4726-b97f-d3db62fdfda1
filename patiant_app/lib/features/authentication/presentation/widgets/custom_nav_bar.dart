import 'package:flutter/material.dart';

class CustomNavBar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onTap;

  const CustomNavBar({
    super.key,
    required this.selectedIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      backgroundColor: Colors.white,
      elevation: 10,
      type: BottomNavigationBarType.fixed,
      currentIndex: selectedIndex,
      selectedItemColor: Colors.blue,
      unselectedItemColor: Colors.grey,
      onTap: (index) {
        String currentRoute = ModalRoute.of(context)?.settings.name ?? '';

        if (index == 0 && currentRoute != '/home') {
          Navigator.pushReplacementNamed(
            context,
            '/home',
            arguments: {'selectedIndex': 0},
          );
        } else if (index == 1 && currentRoute != '/packages') {
          Navigator.pushReplacementNamed(
            context,
            '/packages',
            arguments: {'selectedIndex': 1},
          );
        } else if (index == 2 && currentRoute != '/shop') {
          Navigator.pushReplacementNamed(
            context,
            '/shop',
            arguments: {'selectedIndex': 2},
          );
        } else if (index == 3 && currentRoute != '/profile') {
          Navigator.pushReplacementNamed(
            context,
            '/profile',
            arguments: {'selectedIndex': 3},
          );
        } else {
          onTap(index);
        }
      },
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Acceuil'),
        BottomNavigationBarItem(
            icon: Icon(Icons.local_shipping), label: 'Colis'),
        BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'Panier'), // 🛍️ Updated item
        BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
      ],
    );
  }
}
