import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pinput/pinput.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';
import 'resetPassword_Screen.dart';

class VerifyResetCodeScreen extends StatefulWidget {
  final String email;

  const VerifyResetCodeScreen({super.key, required this.email});

  @override
  _VerifyResetCodeScreenState createState() => _VerifyResetCodeScreenState();
}

class _VerifyResetCodeScreenState extends State<VerifyResetCodeScreen> {
  final TextEditingController codeController = TextEditingController();
  bool isValidCode = false;
  bool isWrongCode = false;
  bool isResending = false;

  void _resendCode() {
    setState(() => isResending = true);

    // Dispatch event to resend the code
    context.read<AuthBloc>().add(ForgotPasswordEvent(widget.email));

    // Simulate an API response delay
    Future.delayed(const Duration(seconds: 2), () {
      setState(() => isResending = false);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text("A new code has been sent to your email.")),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white, // Unifies background color
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 20),
            const Text(
              "Enter verification code",
              style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Text(
              "A verification code was sent to ${widget.email}.",
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),

            // 🔢 PIN Input Field
            BlocListener<AuthBloc, AuthState>(
              listener: (context, state) {
                if (state is VerifyCodeSuccess) {
                  setState(() {
                    isValidCode = true;
                    isWrongCode = false;
                  });

                  Future.delayed(const Duration(milliseconds: 500), () {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ResetPasswordScreen(
                          email: widget.email,
                          code: codeController.text.trim(),
                        ),
                      ),
                    );
                  });
                } else if (state is AuthError) {
                  setState(() {
                    isWrongCode = true;
                    isValidCode = false;
                  });
                }
              },
              child: Pinput(
                length: 6,
                controller: codeController,
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  setState(() {
                    isWrongCode = false;
                    isValidCode = false;
                  });

                  if (value.length == 6) {
                    context.read<AuthBloc>().add(
                          VerifyResetCodeEvent(widget.email, value),
                        );
                  }
                },
                defaultPinTheme: PinTheme(
                  width: 50,
                  height: 50,
                  textStyle: const TextStyle(
                      fontSize: 24, fontWeight: FontWeight.bold),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.grey.shade400),
                  ),
                ),
                focusedPinTheme: PinTheme(
                  width: 50,
                  height: 50,
                  textStyle: const TextStyle(
                      fontSize: 24, fontWeight: FontWeight.bold),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.blue),
                  ),
                ),
                submittedPinTheme: PinTheme(
                  width: 50,
                  height: 50,
                  textStyle: const TextStyle(
                      fontSize: 24, fontWeight: FontWeight.bold),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                        color: isValidCode
                            ? Colors.green
                            : isWrongCode
                                ? Colors.red
                                : Colors.grey),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),

            // 🔄 Resend Code Button
            TextButton(
              onPressed: isResending ? null : _resendCode,
              child: isResending
                  ? const CircularProgressIndicator()
                  : const Text(
                      "Resend code",
                      style: TextStyle(color: Colors.green, fontSize: 16),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
