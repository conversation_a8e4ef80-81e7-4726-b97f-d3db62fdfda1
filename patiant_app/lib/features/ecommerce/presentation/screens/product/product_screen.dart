import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/ecommerce/data/repositories/product_repository.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_event.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_state.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_event.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';

class ProductScreen extends StatefulWidget {
  const ProductScreen({super.key});

  @override
  State<ProductScreen> createState() => _ProductScreenState();
}

class _ProductScreenState extends State<ProductScreen> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();

  String? selectedCategoryId;
  String? selectedCategoryName;
  List<Map<String, dynamic>> categoryList = [];

  int _selectedIndex = 0;
  String? profilePhoto;
  String firstName = "";
  String lastName = "";

  /// Tracks the current image index per product card
  final Map<int, int> currentImageIndex = {};

  @override
  void initState() {
    super.initState();
    context.read<ProductBloc>().add(LoadProducts());
    context.read<ProfileBloc>().add(LoadProfile());

    context.read<ProfileBloc>().stream.listen((state) {
      if (state is ProfileLoaded) {
        setState(() {
          profilePhoto = state.profilePhoto;
          firstName = state.firstName;
          lastName = state.lastName;
        });
      }
    });

    ProductRepository().getAllCategories().then((cats) {
      setState(() {
        categoryList = cats;
      });
    });
  }

  void _loadProducts() {
    setState(() {});
    context.read<ProductBloc>().add(
          LoadProducts(
            search: _searchController.text,
            categoryId: selectedCategoryId,
            minPrice: double.tryParse(_minPriceController.text),
            maxPrice: double.tryParse(_maxPriceController.text),
          ),
        );
  }

  void _openCategoryFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.5,
          minChildSize: 0.3,
          maxChildSize: 0.8,
          builder: (_, scrollController) {
            return StatefulBuilder(builder: (context, setModalState) {
              return Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Text(
                      "Choisir une catégorie",
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const Divider(),
                    Expanded(
                      child: ListView.builder(
                        controller: scrollController,
                        itemCount: categoryList.length,
                        itemBuilder: (_, index) {
                          final cat = categoryList[index];
                          final isSelected = selectedCategoryId == cat['id'];
                          return ListTile(
                            title: Text(cat['name']),
                            trailing: isSelected
                                ? const Icon(Icons.check_circle,
                                    color: Colors.blue)
                                : null,
                            onTap: () {
                              setModalState(() {
                                selectedCategoryId =
                                    isSelected ? null : cat['id'];
                                selectedCategoryName =
                                    isSelected ? null : cat['name'];
                              });
                            },
                          );
                        },
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        _loadProducts();
                        Navigator.pop(context);
                      },
                      child: const Text("Voir les résultats"),
                    ),
                  ],
                ),
              );
            });
          },
        );
      },
    );
  }

  void _resetAllFilters() {
    setState(() {
      selectedCategoryId = null;
      selectedCategoryName = null;
      _minPriceController.clear();
      _maxPriceController.clear();
      _searchController.clear();
    });
    _loadProducts();
  }

  bool _isAnyFilterApplied() {
    return selectedCategoryId != null ||
        _searchController.text.isNotEmpty ||
        _minPriceController.text.isNotEmpty ||
        _maxPriceController.text.isNotEmpty;
  }

  void _showImageFullScreen(BuildContext context, List<dynamic> imagePaths) {
    showDialog(
      context: context,
      builder: (_) => Dialog(
        backgroundColor: Colors.black,
        insetPadding: const EdgeInsets.all(16),
        child: PageView.builder(
          itemCount: imagePaths.length,
          itemBuilder: (context, index) {
            final imageUrl = 'http://***********:3000${imagePaths[index]}';
            return InteractiveViewer(
              child: Image.network(imageUrl, fit: BoxFit.contain),
            );
          },
        ),
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required String? selectedLabel,
    required VoidCallback onTap,
    required VoidCallback onClear,
  }) {
    return selectedLabel != null
        ? Chip(
            label: Text(selectedLabel),
            deleteIcon: const Icon(Icons.close),
            onDeleted: onClear,
            backgroundColor: Colors.blue.shade100,
          )
        : OutlinedButton(
            onPressed: onTap,
            child: Text(label),
          );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, notificationState) {
        int unreadCount = 0;
        if (notificationState is NotificationLoaded) {
          unreadCount = notificationState.unreadCount;
        } else if (notificationState is UnreadCountLoaded) {
          unreadCount = notificationState.count;
        }

        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            backgroundColor: const Color(0xFFF6F9FC),
            appBar: CustomAppBar(
              logoPath: 'assets/logo.png',
              notificationCount: unreadCount > 0 ? unreadCount : null,
              onNotificationTap: () {
                Navigator.pushNamed(context, '/notifications');
              },
              onProfileTap: () =>
                  Navigator.pushReplacementNamed(context, '/profile'),
              profilePhoto: profilePhoto,
              firstName: firstName,
              lastName: lastName,
            ),
            body: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    children: [
                      TextField(
                        controller: _searchController,
                        onChanged: (_) => _loadProducts(),
                        decoration: const InputDecoration(
                          labelText: 'Rechercher un produit',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _minPriceController,
                              onChanged: (_) => _loadProducts(),
                              keyboardType: TextInputType.number,
                              decoration: const InputDecoration(
                                labelText: 'Prix min',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: TextField(
                              controller: _maxPriceController,
                              onChanged: (_) => _loadProducts(),
                              keyboardType: TextInputType.number,
                              decoration: const InputDecoration(
                                labelText: 'Prix max',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Wrap(
                        spacing: 10,
                        runSpacing: 10,
                        children: [
                          _buildFilterChip(
                            label: "Catégorie",
                            selectedLabel: selectedCategoryName,
                            onTap: _openCategoryFilterSheet,
                            onClear: () {
                              setState(() {
                                selectedCategoryId = null;
                                selectedCategoryName = null;
                              });
                              _loadProducts();
                            },
                          ),
                          if (_isAnyFilterApplied())
                            OutlinedButton.icon(
                              onPressed: _resetAllFilters,
                              icon: const Icon(Icons.refresh),
                              label: const Text("Réinitialiser"),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: BlocBuilder<ProductBloc, ProductState>(
                    builder: (context, state) {
                      if (state is ProductLoading) {
                        return const Center(child: CircularProgressIndicator());
                      } else if (state is ProductsLoaded) {
                        return GridView.builder(
                          padding: const EdgeInsets.all(10),
                          itemCount: state.products.length,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            mainAxisSpacing: 16,
                            crossAxisSpacing: 16,
                            childAspectRatio: 0.65,
                          ),
                          itemBuilder: (context, index) {
                            final product = state.products[index];
                            final discount = product['discount'] ?? 0.0;
                            final price = product['price'] ?? 0.0;
                            final discountedPrice =
                                price - (price * (discount / 100));
                            final images =
                                product['storagePath'] as List<dynamic>? ?? [];

                            currentImageIndex.putIfAbsent(index, () => 0);

                            return Stack(
                              children: [
                                GestureDetector(
                                  onTap: () async {
                                    await Navigator.pushNamed(
                                      context,
                                      '/productDetails',
                                      arguments: product['id'],
                                    );
                                    _loadProducts();
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(12),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.05),
                                          blurRadius: 6,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    padding: const EdgeInsets.all(12),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        GestureDetector(
                                          onTap: () => _showImageFullScreen(
                                              context, images),
                                          child: Column(
                                            children: [
                                              ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                child: SizedBox(
                                                  height: 130,
                                                  width: double.infinity,
                                                  child: PageView.builder(
                                                    itemCount: images.length,
                                                    onPageChanged: (imgIndex) {
                                                      setState(() {
                                                        currentImageIndex[
                                                            index] = imgIndex;
                                                      });
                                                    },
                                                    itemBuilder: (_, imgIndex) {
                                                      final imgUrl =
                                                          'http://***********:3000${images[imgIndex]}';
                                                      return Image.network(
                                                        imgUrl,
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (_, __,
                                                                ___) =>
                                                            const Icon(Icons
                                                                .broken_image),
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ),
                                              if (images.length > 1)
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          top: 6),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: List.generate(
                                                        images.length,
                                                        (dotIndex) {
                                                      final isActive =
                                                          currentImageIndex[
                                                                  index] ==
                                                              dotIndex;
                                                      return Container(
                                                        margin: const EdgeInsets
                                                            .symmetric(
                                                            horizontal: 3),
                                                        width: 8,
                                                        height: 8,
                                                        decoration:
                                                            BoxDecoration(
                                                          color: isActive
                                                              ? Colors.blue
                                                              : Colors
                                                                  .grey[300],
                                                          shape:
                                                              BoxShape.circle,
                                                        ),
                                                      );
                                                    }),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        Text(
                                          product['name'] ?? '',
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: const TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 6),
                                        if (discount > 0) ...[
                                          Text(
                                            "${price.toStringAsFixed(2)} \$CAD",
                                            style: const TextStyle(
                                              fontSize: 13,
                                              color: Colors.grey,
                                              decoration:
                                                  TextDecoration.lineThrough,
                                            ),
                                          ),
                                          Text(
                                            "${discountedPrice.toStringAsFixed(2)} \$CAD",
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.green,
                                            ),
                                          ),
                                          Text(
                                            "Économisez ${discount.toInt()}%",
                                            style: const TextStyle(
                                              fontSize: 11,
                                              color: Colors.red,
                                            ),
                                          ),
                                        ] else
                                          Text(
                                            "${price.toStringAsFixed(2)} \$CAD",
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                                if (discount > 0)
                                  Positioned(
                                    top: 8,
                                    right: 8,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.red,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: const Text(
                                        "Réduit",
                                        style: TextStyle(
                                            color: Colors.white, fontSize: 12),
                                      ),
                                    ),
                                  ),
                              ],
                            );
                          },
                        );
                      }
                      return const Center(
                          child: Text("Une erreur s’est produite."));
                    },
                  ),
                ),
              ],
            ),
            bottomNavigationBar: CustomNavBar(
              selectedIndex: _selectedIndex,
              onTap: (index) => setState(() => _selectedIndex = index),
            ),
          ),
        );
      },
    );
  }
}
