import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/ecommerce/data/repositories/product_repository.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_event.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_state.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_event.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_state.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_event.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/profile/presentation/screens/profile_screen.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';

class ProductDetailsScreen extends StatefulWidget {
  final String productId;
  final int selectedIndex;

  const ProductDetailsScreen({
    super.key,
    required this.productId,
    this.selectedIndex = 0,
  });

  @override
  State<ProductDetailsScreen> createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends State<ProductDetailsScreen> {
  String? profilePhoto;
  String firstName = '';
  String lastName = '';
  String? categoryName;

  @override
  void initState() {
    super.initState();
    context.read<ProfileBloc>().add(LoadProfile());
    context.read<ProductBloc>().add(LoadProductDetails(widget.productId));
    context.read<OrderBloc>().add(LoadBasket());

    context.read<ProfileBloc>().stream.listen((state) {
      if (state is ProfileLoaded) {
        setState(() {
          profilePhoto = state.profilePhoto;
          firstName = state.firstName;
          lastName = state.lastName;
        });
      }
    });
  }

  void _showImageFullScreen(BuildContext context, String imageUrl) {
    showDialog(
      context: context,
      builder: (_) => Dialog(
        insetPadding: const EdgeInsets.all(16),
        child: InteractiveViewer(
          child: Image.network(imageUrl),
        ),
      ),
    );
  }

  Widget _buildDetail(String title, String? value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "$title: ",
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Expanded(child: Text(value ?? "N/A")),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, notificationState) {
        int unreadCount = 0;
        if (notificationState is NotificationLoaded) {
          unreadCount = notificationState.unreadCount;
        } else if (notificationState is UnreadCountLoaded) {
          unreadCount = notificationState.count;
        }

        return Scaffold(
          backgroundColor: const Color(0xFFF6F9FC),
          appBar: CustomAppBar(
            logoPath: 'assets/logo.png',
            profilePhoto: profilePhoto,
            firstName: firstName,
            lastName: lastName,
            notificationCount: unreadCount > 0 ? unreadCount : null,
            onNotificationTap: () {
              Navigator.pushNamed(context, '/notifications');
            },
            onProfileTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => BlocProvider.value(
                    value: context.read<ProfileBloc>(),
                    child: const ProfileScreen(),
                  ),
                ),
              );
            },
          ),
          body: BlocBuilder<ProductBloc, ProductState>(
            builder: (context, state) {
              if (state is ProductLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (state is ProductDetailsLoaded) {
                final product = state.product;
                final discount = product['discount'] ?? 0;
                final price = product['price'] ?? 0.0;
                final discountedPrice = price - (price * (discount / 100));
                final imageUrl = product['storagePath'] != null &&
                        product['storagePath'].isNotEmpty
                    ? 'http://172.20.10.4:3000${product['storagePath'][0]}'
                    : null;

                if (categoryName == null && product['categoryId'] != null) {
                  ProductRepository()
                      .getCategoryById(product['categoryId'])
                      .then((cat) {
                    setState(() {
                      categoryName = cat?['name'];
                    });
                  });
                }

                return Stack(
                  children: [
                    SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.arrow_back),
                                onPressed: () {
                                  Navigator.pop(context);
                                  context
                                      .read<ProductBloc>()
                                      .add(LoadProducts());
                                },
                              ),
                              const Expanded(
                                child: Center(
                                  child: Text(
                                    "Détail du produit",
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 48),
                            ],
                          ),
                          const SizedBox(height: 20),
                          if (imageUrl != null)
                            GestureDetector(
                              onTap: () =>
                                  _showImageFullScreen(context, imageUrl),
                              child: Center(
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Image.network(
                                    imageUrl,
                                    width: double.infinity,
                                    height: 200,
                                    fit: BoxFit.cover,
                                    errorBuilder: (_, __, ___) =>
                                        const Icon(Icons.broken_image),
                                  ),
                                ),
                              ),
                            ),
                          const SizedBox(height: 20),
                          Text(
                            product['name'] ?? 'Nom inconnu',
                            style: const TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              if (discount > 0) ...[
                                Text(
                                  "${price.toStringAsFixed(2)} \$CAD",
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  "${discountedPrice.toStringAsFixed(2)} \$CAD",
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.redAccent,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    "-$discount%",
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ] else
                                Text(
                                  "${price.toStringAsFixed(2)} \$CAD",
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (product['description'] != null)
                            Text(
                              product['description'],
                              style: const TextStyle(fontSize: 16),
                            ),
                          const Divider(height: 30),
                          _buildDetail(
                              "Statut",
                              product['stockStatus'] == 'IN_STOCK'
                                  ? 'En stock'
                                  : product['stockStatus'] == 'OUT_OF_STOCK'
                                      ? 'Rupture de stock'
                                      : product['stockStatus']),
                          _buildDetail("Catégorie", categoryName),
                          _buildDetail("Fournisseur", product['supplier']),
                          _buildDetail(
                            "Taxable",
                            product['isTaxable'] == true ? "Oui" : "Non",
                          ),
                          if (product['expiryDate'] != null)
                            _buildDetail(
                              "Date d’expiration",
                              product['expiryDate'].toString().substring(0, 10),
                            ),
                          const SizedBox(height: 30),

                          // ✅ BlocBuilder to control Add to Cart button
                          BlocBuilder<OrderBloc, OrderState>(
                            builder: (context, orderState) {
                              bool _canAddToCart = true;
                              bool _showCartButton = false;
                              String? disabledMessage;

                              // Check if product is out of stock
                              final stockStatus = product['stockStatus'];
                              if (stockStatus == 'OUT_OF_STOCK') {
                                _canAddToCart = false;
                                disabledMessage =
                                    'Ce produit est en rupture de stock.';
                              }

                              if (orderState is OrderLoaded) {
                                final productIds =
                                    orderState.basket['productIds'] as List?;
                                final flag = orderState.basket['flag'] ?? false;
                                final status = orderState.basket['status'];

                                if (flag == true && status == 'APPROVED') {
                                  _canAddToCart = false;
                                  disabledMessage =
                                      'Une commande est déjà en attente de livraison. Veuillez attendre sa livraison avant d\'ajouter un nouveau produit.';
                                } else if (productIds != null &&
                                    productIds.contains(widget.productId) &&
                                    flag == false) {
                                  _showCartButton = true;
                                }
                              }

                              return !_showCartButton
                                  ? ElevatedButton.icon(
                                      icon: const Icon(Icons.add_shopping_cart),
                                      label: const Text("Ajouter au panier"),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            _canAddToCart ? null : Colors.grey,
                                      ),
                                      onPressed: _canAddToCart
                                          ? () {
                                              context.read<OrderBloc>().add(
                                                  AddToBasket(
                                                      widget.productId));
                                            }
                                          : () {
                                              if (disabledMessage != null) {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(SnackBar(
                                                  content: Text(
                                                    'Une commande est déjà en attente de livraison. Veuillez attendre sa livraison avant d’ajouter un nouveau produit.',
                                                  ),
                                                ));
                                              }
                                            },
                                    )
                                  : const SizedBox();
                            },
                          ),

                          const SizedBox(height: 80),
                        ],
                      ),
                    ),

                    // ✅ Show "Voir le panier" based on actual state
                    BlocBuilder<OrderBloc, OrderState>(
                      builder: (context, orderState) {
                        bool _showCartButton = false;

                        if (orderState is OrderLoaded) {
                          final productIds =
                              orderState.basket['productIds'] as List?;
                          final flag = orderState.basket['flag'] ?? false;

                          if (productIds != null &&
                              productIds.contains(widget.productId) &&
                              flag == false) {
                            _showCartButton = true;
                          }
                        }

                        return _showCartButton
                            ? Positioned(
                                bottom: 20,
                                left: 16,
                                right: 16,
                                child: ElevatedButton.icon(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 14),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                  icon: const Icon(Icons.shopping_cart),
                                  label: const Text("Voir le panier"),
                                  onPressed: () {
                                    Navigator.pushNamed(context, '/shop');
                                  },
                                ),
                              )
                            : const SizedBox();
                      },
                    ),
                  ],
                );
              } else if (state is ProductError) {
                return Center(child: Text(state.message));
              } else {
                return const Center(child: Text("Une erreur s’est produite."));
              }
            },
          ),
          bottomNavigationBar: CustomNavBar(
            selectedIndex: widget.selectedIndex,
            onTap: (index) {},
          ),
        );
      },
    );
  }
}
