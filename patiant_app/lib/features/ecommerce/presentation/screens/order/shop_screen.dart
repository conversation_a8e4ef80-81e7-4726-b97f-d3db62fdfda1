import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_event.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_state.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_event.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_state.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';

class ShopScreen extends StatefulWidget {
  const ShopScreen({super.key});

  @override
  State<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends State<ShopScreen> {
  int _selectedIndex = 2;
  String? profilePhoto;
  String firstName = "";
  String lastName = "";
  int quantity = 1;

  @override
  void initState() {
    super.initState();
    final profileState = context.read<ProfileBloc>().state;
    if (profileState is ProfileLoaded) {
      profilePhoto = profileState.profilePhoto;
      firstName = profileState.firstName;
      lastName = profileState.lastName;
    }

    context.read<OrderBloc>().add(LoadBasket());
  }

  void _confirmDelete(String productId) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text("Supprimer du panier"),
        content: const Text("Voulez-vous vraiment supprimer ce produit ?"),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("Annuler")),
          TextButton(
            onPressed: () {
              context.read<OrderBloc>().add(RemoveFromBasket(productId));
              Navigator.pop(context);
            },
            child: const Text("Confirmer", style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, notificationState) {
        int unreadCount = 0;
        if (notificationState is NotificationLoaded) {
          unreadCount = notificationState.unreadCount;
        } else if (notificationState is UnreadCountLoaded) {
          unreadCount = notificationState.count;
        }

        return Scaffold(
          appBar: CustomAppBar(
            logoPath: 'assets/logo.png',
            profilePhoto: profilePhoto,
            firstName: firstName,
            lastName: lastName,
            notificationCount: unreadCount > 0 ? unreadCount : null,
            onNotificationTap: () {
              Navigator.pushNamed(context, '/notifications');
            },
            onProfileTap: () => Navigator.pushNamed(context, '/profile'),
          ),
          body: BlocBuilder<OrderBloc, OrderState>(
            builder: (context, orderState) {
              if (orderState is OrderLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (orderState is OrderLoaded) {
                final basket = orderState.basket;
                final productIds = basket['productIds'] as List?;
                final isEditable = basket['flag'] == false;

                if (productIds == null || productIds.isEmpty) {
                  return const Center(child: Text("Votre panier est vide."));
                }

                final productId = productIds.first;
                context.read<ProductBloc>().add(LoadProductDetails(productId));

                return BlocBuilder<ProductBloc, ProductState>(
                  builder: (context, productState) {
                    if (productState is ProductDetailsLoaded) {
                      final product = productState.product;
                      final discount = product['discount'] ?? 0;
                      final price = product['price'] ?? 0.0;
                      final discountedPrice =
                          price - (price * (discount.toDouble() / 100));
                      final imageUrl = product['storagePath']?.isNotEmpty ==
                              true
                          ? 'http://172.20.10.4:3000${product['storagePath'][0]}'
                          : null;

                      return StatefulBuilder(
                        builder: (context, setLocalState) {
                          return Stack(
                            children: [
                              ListView(
                                padding: const EdgeInsets.all(16),
                                children: [
                                  const Text("Votre panier",
                                      style: TextStyle(
                                          fontSize: 22,
                                          fontWeight: FontWeight.bold)),
                                  const SizedBox(height: 10),
                                  Card(
                                    elevation: 3,
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(12)),
                                    child: ListTile(
                                      contentPadding: const EdgeInsets.all(12),
                                      leading: ClipRRect(
                                        borderRadius: BorderRadius.circular(8),
                                        child: imageUrl != null
                                            ? Image.network(
                                                imageUrl,
                                                width: 60,
                                                height: 60,
                                                fit: BoxFit.cover,
                                              )
                                            : const Icon(Icons.image),
                                      ),
                                      title: Text(product['name']),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          if (discount > 0)
                                            Row(
                                              children: [
                                                Flexible(
                                                  child: Text(
                                                    "${(price * quantity).toStringAsFixed(2)} \$CAD",
                                                    style: const TextStyle(
                                                      color: Colors.grey,
                                                      decoration: TextDecoration
                                                          .lineThrough,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                                const SizedBox(width: 5),
                                                Flexible(
                                                  child: Text(
                                                    "${(discountedPrice * quantity).toStringAsFixed(2)} \$CAD",
                                                    style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.green,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                              ],
                                            )
                                          else
                                            Text(
                                              "${(price * quantity).toStringAsFixed(2)} \$CAD",
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          const SizedBox(height: 6),
                                          if (discount > 0)
                                            Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 6,
                                                      vertical: 2),
                                              decoration: BoxDecoration(
                                                color: Colors.orange,
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: Text("-$discount%",
                                                  style: const TextStyle(
                                                      color: Colors.white)),
                                            ),
                                          if (!isEditable)
                                            const SizedBox(height: 6),
                                          if (!isEditable)
                                            const LinearProgressIndicator(
                                              color: Colors.grey,
                                              minHeight: 3,
                                            )
                                        ],
                                      ),
                                      trailing: isEditable
                                          ? Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                quantity > 1
                                                    ? IconButton(
                                                        icon: const Icon(
                                                            Icons.remove),
                                                        onPressed: () {
                                                          setLocalState(() {
                                                            quantity--;
                                                          });
                                                        },
                                                      )
                                                    : IconButton(
                                                        icon: const Icon(
                                                            Icons.delete,
                                                            color: Colors.red),
                                                        onPressed: () =>
                                                            _confirmDelete(
                                                                productId),
                                                      ),
                                                Text(quantity.toString()),
                                                IconButton(
                                                  icon: const Icon(Icons.add),
                                                  onPressed: () {
                                                    final availableQuantity =
                                                        product['quantity'] ??
                                                            product['stock'] ??
                                                            product[
                                                                'availableQuantity'] ??
                                                            999;
                                                    if (quantity <
                                                        availableQuantity) {
                                                      setLocalState(() {
                                                        quantity++;
                                                      });
                                                    } else {
                                                      ScaffoldMessenger.of(
                                                              context)
                                                          .showSnackBar(
                                                        SnackBar(
                                                          content: Text(
                                                              'Stock maximum atteint ($availableQuantity disponible)'),
                                                          backgroundColor:
                                                              Colors.orange,
                                                        ),
                                                      );
                                                    }
                                                  },
                                                ),
                                              ],
                                            )
                                          : null,
                                      onTap: () => Navigator.pushNamed(
                                        context,
                                        '/shopDetails',
                                        arguments: {
                                          'productId': productId,
                                          'quantity': quantity,
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              if (isEditable)
                                Positioned(
                                  bottom: 0,
                                  left: 0,
                                  right: 0,
                                  child: Container(
                                    padding: const EdgeInsets.all(16),
                                    child: ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.teal,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 16),
                                      ),
                                      onPressed: () {
                                        Navigator.pushNamed(
                                          context,
                                          '/shopDetails',
                                          arguments: {
                                            'productId': productId,
                                            'quantity': quantity,
                                          },
                                        );
                                      },
                                      child: Text(
                                        discount > 0
                                            ? "Passer la commande - ${(discountedPrice * quantity).toStringAsFixed(2)} \$CAD"
                                            : "Passer la commande - ${(price * quantity).toStringAsFixed(2)} \$CAD",
                                      ),
                                    ),
                                  ),
                                )
                            ],
                          );
                        },
                      );
                    } else if (productState is ProductLoading) {
                      return const Center(child: CircularProgressIndicator());
                    } else {
                      return const Center(
                          child: Text("Votre panier est vide."));
                    }
                  },
                );
              }

              return const Center(child: Text("Votre panier est vide."));
            },
          ),
          bottomNavigationBar: CustomNavBar(
            selectedIndex: _selectedIndex,
            onTap: (index) => setState(() => _selectedIndex = index),
          ),
        );
      },
    );
  }
}
