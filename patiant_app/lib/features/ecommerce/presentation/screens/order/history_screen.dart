import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_event.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/order/order_state.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_bloc.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_event.dart';
import 'package:patiant_app/features/ecommerce/presentation/bloc/product/product_state.dart';
import 'package:patiant_app/features/ecommerce/data/repositories/product_repository.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  int _selectedIndex = 3;
  String? profilePhoto;
  String firstName = "";
  String lastName = "";

  @override
  void initState() {
    super.initState();
    context.read<OrderBloc>().add(LoadOrderHistory());

    final profileState = context.read<ProfileBloc>().state;
    if (profileState is ProfileLoaded) {
      profilePhoto = profileState.profilePhoto;
      firstName = profileState.firstName;
      lastName = profileState.lastName;
    }
  }

  void _showImageFullScreen(String imageUrl) {
    showDialog(
      context: context,
      builder: (_) => Dialog(
        insetPadding: const EdgeInsets.all(16),
        child: InteractiveViewer(
          child: Image.network(imageUrl),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, notificationState) {
        int unreadCount = 0;
        if (notificationState is NotificationLoaded) {
          unreadCount = notificationState.unreadCount;
        } else if (notificationState is UnreadCountLoaded) {
          unreadCount = notificationState.count;
        }

        return Scaffold(
          appBar: CustomAppBar(
            logoPath: 'assets/logo.png',
            profilePhoto: profilePhoto,
            firstName: firstName,
            lastName: lastName,
            notificationCount: unreadCount > 0 ? unreadCount : null,
            onNotificationTap: () {
              Navigator.pushNamed(context, '/notifications');
            },
            onProfileTap: () => Navigator.pushNamed(context, '/profile'),
          ),
          body: BlocBuilder<OrderBloc, OrderState>(
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back),
                        onPressed: () => Navigator.pop(context),
                      ),
                      const Expanded(
                        child: Center(
                          child: Text(
                            "Historique des commandes",
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                          width: 48), // spacer to balance back button
                    ],
                  ),
                  const SizedBox(height: 6),
                  Expanded(
                    child: Builder(builder: (context) {
                      if (state is OrderLoading) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      if (state is OrderHistoryLoaded) {
                        final orders = state.orders;

                        if (orders.isEmpty) {
                          return const Center(
                              child: Text("Aucune commande livrée."));
                        }

                        return ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: orders.length,
                          itemBuilder: (context, index) {
                            final order = orders[index];
                            final productId =
                                (order['productIds'] as List?)?.first;
                            final quantity = order['quantity'] ?? 1;
                            final note = order['deliveryManNote'];
                            final deliveredAt =
                                order['deliveredAt'] ?? order['orderDate'];

                            return BlocProvider(
                              create: (_) => ProductBloc(ProductRepository())
                                ..add(LoadProductDetails(productId)),
                              child: BlocBuilder<ProductBloc, ProductState>(
                                builder: (context, productState) {
                                  if (productState is ProductDetailsLoaded) {
                                    final product = productState.product;
                                    final imageUrl = product['storagePath']
                                                ?.isNotEmpty ==
                                            true
                                        ? 'http://172.20.10.4:3000${product['storagePath'][0]}'
                                        : null;

                                    final price = product['price'] ?? 0.0;
                                    final discount = product['discount'] ?? 0;
                                    final discountedPrice =
                                        price - (price * (discount / 100));
                                    final totalDiscounted =
                                        discountedPrice * quantity;
                                    final totalOriginal = price * quantity;

                                    return Card(
                                      margin: const EdgeInsets.only(bottom: 16),
                                      elevation: 3,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: ListTile(
                                        contentPadding:
                                            const EdgeInsets.all(12),
                                        leading: GestureDetector(
                                          onTap: () {
                                            if (imageUrl != null) {
                                              _showImageFullScreen(imageUrl);
                                            }
                                          },
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            child: imageUrl != null
                                                ? Image.network(
                                                    imageUrl,
                                                    width: 60,
                                                    height: 60,
                                                    fit: BoxFit.cover,
                                                  )
                                                : const Icon(
                                                    Icons.image_not_supported),
                                          ),
                                        ),
                                        title:
                                            Text(product['name'] ?? "Produit"),
                                        subtitle: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const SizedBox(height: 4),
                                            Row(
                                              children: [
                                                if (discount > 0)
                                                  Text(
                                                    "${totalOriginal.toStringAsFixed(2)} \$CAD",
                                                    style: const TextStyle(
                                                      decoration: TextDecoration
                                                          .lineThrough,
                                                      color: Colors.grey,
                                                    ),
                                                  ),
                                                const SizedBox(width: 6),
                                                Text(
                                                  "${totalDiscounted.toStringAsFixed(2)} \$CAD",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.green,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Text("Quantité : $quantity"),
                                            if (note != null && note.isNotEmpty)
                                              Text("Note : $note"),
                                            if (deliveredAt != null)
                                              Text(
                                                "Livrée le : ${DateFormat('yyyy-MM-dd').format(DateTime.parse(deliveredAt))}",
                                              ),
                                          ],
                                        ),
                                      ),
                                    );
                                  } else if (productState is ProductError) {
                                    return const ListTile(
                                      title: Text(
                                          "Erreur de chargement du produit."),
                                    );
                                  } else {
                                    return const Padding(
                                      padding: EdgeInsets.all(8),
                                      child: Center(
                                          child: CircularProgressIndicator()),
                                    );
                                  }
                                },
                              ),
                            );
                          },
                        );
                      }

                      if (state is OrderError) {
                        return Center(child: Text(state.error));
                      }

                      return const Center(
                          child: Text("Une erreur s’est produite."));
                    }),
                  ),
                ],
              );
            },
          ),
          bottomNavigationBar: CustomNavBar(
            selectedIndex: _selectedIndex,
            onTap: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
          ),
        );
      },
    );
  }
}
