import 'package:flutter_bloc/flutter_bloc.dart';
import 'order_event.dart';
import 'order_state.dart';
import 'package:patiant_app/features/ecommerce/data/repositories/order_repository.dart';

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final OrderRepository orderRepository;

  OrderBloc(this.orderRepository) : super(OrderInitial()) {
    on<LoadBasket>((event, emit) async {
      emit(OrderLoading());
      try {
        final basket = await orderRepository.getBasket();
        if (basket != null) {
          emit(OrderLoaded(basket));
        } else {
          emit(OrderSuccess("Aucun panier actif"));
        }
      } catch (e) {
        emit(OrderError("Erreur lors du chargement du panier"));
      }
    });

    on<AddToBasket>((event, emit) async {
      try {
        final basket = await orderRepository.getBasket();

        if (basket != null &&
            basket['flag'] == true &&
            basket['status'] == 'APPROVED') {
          emit(OrderPendingDelivery(
              "Une commande est déjà en attente de livraison. Veuillez attendre sa livraison avant d’ajouter un nouveau produit."));
          return;
        }

        await orderRepository.addToBasket(event.productId);
        emit(OrderSuccess("Produit ajouté au panier"));
        await Future.delayed(const Duration(milliseconds: 500));
        add(LoadBasket());
      } catch (e) {
        emit(OrderError("Erreur lors de l'ajout au panier"));
      }
    });

    on<RemoveFromBasket>((event, emit) async {
      emit(OrderLoading());
      try {
        await orderRepository.removeFromBasket(event.productId);
        add(LoadBasket());
      } catch (e) {
        emit(OrderError("Erreur lors de la suppression du panier"));
      }
    });

    on<ConfirmOrder>((event, emit) async {
      emit(OrderLoading());
      try {
        await orderRepository.confirmOrder(
            event.quantity, event.deliveryManNote);
        emit(OrderSuccess("Commande confirmée avec succès."));
      } catch (e) {
        emit(OrderError("Erreur lors de la confirmation de commande"));
      }
    });

    on<LoadOrderHistory>((event, emit) async {
      emit(OrderLoading());
      try {
        final orders = await orderRepository.getOrderHistory();
        emit(OrderHistoryLoaded(orders));
      } catch (e) {
        emit(OrderError("Erreur lors du chargement de l'historique"));
      }
    });
  }
}
