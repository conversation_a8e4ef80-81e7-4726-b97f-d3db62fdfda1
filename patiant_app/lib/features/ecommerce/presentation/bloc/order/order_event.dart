import 'package:equatable/equatable.dart';

abstract class OrderEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

// ✅ Get current basket
class LoadBasket extends OrderEvent {}

// ✅ Add product to basket
class AddToBasket extends OrderEvent {
  final String productId;

  AddToBasket(this.productId);

  @override
  List<Object?> get props => [productId];
}

// ✅ Remove product from basket
class RemoveFromBasket extends OrderEvent {
  final String productId;

  RemoveFromBasket(this.productId);

  @override
  List<Object?> get props => [productId];
}

class ConfirmOrder extends OrderEvent {
  final int quantity;
  final String? deliveryManNote;

  ConfirmOrder({required this.quantity, this.deliveryManNote});

  @override
  List<Object?> get props => [quantity, deliveryManNote];
}

class LoadOrderHistory extends OrderEvent {}
