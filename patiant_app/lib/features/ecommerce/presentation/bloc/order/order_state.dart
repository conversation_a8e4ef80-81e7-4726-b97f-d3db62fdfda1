import 'package:equatable/equatable.dart';

abstract class OrderState extends Equatable {
  @override
  List<Object?> get props => [];
}

class OrderInitial extends OrderState {}

class OrderLoading extends OrderState {}

class OrderLoaded extends OrderState {
  final Map<String, dynamic> basket;

  OrderLoaded(this.basket);

  @override
  List<Object?> get props => [basket];
}

class OrderSuccess extends OrderState {
  final String message;

  OrderSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class OrderError extends OrderState {
  final String error;

  OrderError(this.error);

  @override
  List<Object?> get props => [error];
}

/// 🔒 This state is used when an order is pending delivery and user tries to add another product
class OrderPendingDelivery extends OrderState {
  final String message;

  OrderPendingDelivery(this.message);

  @override
  List<Object?> get props => [message];
}

class OrderHistoryLoaded extends OrderState {
  final List<Map<String, dynamic>> orders;

  OrderHistoryLoaded(this.orders);

  @override
  List<Object?> get props => [orders];
}
