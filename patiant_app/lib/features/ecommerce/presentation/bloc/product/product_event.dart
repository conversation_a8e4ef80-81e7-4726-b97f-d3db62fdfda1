import 'package:equatable/equatable.dart';

abstract class ProductEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

// ✅ Load all products with filters
class LoadProducts extends ProductEvent {
  final String? search;
  final String? categoryId;
  final double? minPrice;
  final double? maxPrice;

  LoadProducts({
    this.search,
    this.categoryId,
    this.minPrice,
    this.maxPrice,
  });

  @override
  List<Object?> get props => [search, categoryId, minPrice, maxPrice];
}

// ✅ Load a single product by ID
class LoadProductDetails extends ProductEvent {
  final String productId;

  LoadProductDetails(this.productId);

  @override
  List<Object?> get props => [productId];
}
