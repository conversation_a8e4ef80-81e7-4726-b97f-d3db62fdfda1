import 'package:equatable/equatable.dart';

abstract class ProductState extends Equatable {
  @override
  List<Object?> get props => [];
}

class ProductInitial extends ProductState {}

class ProductLoading extends ProductState {}

class ProductsLoaded extends ProductState {
  final List<Map<String, dynamic>> products;

  ProductsLoaded(this.products);

  @override
  List<Object?> get props => [products];
}

class ProductDetailsLoaded extends ProductState {
  final Map<String, dynamic> product;

  ProductDetailsLoaded(this.product);

  @override
  List<Object?> get props => [product];
}

class ProductError extends ProductState {
  final String message;

  ProductError(this.message);

  @override
  List<Object?> get props => [message];
}
