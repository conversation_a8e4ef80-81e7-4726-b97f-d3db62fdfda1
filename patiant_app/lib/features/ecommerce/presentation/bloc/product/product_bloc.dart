import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/ecommerce/data/repositories/product_repository.dart';
import 'product_event.dart';
import 'product_state.dart';

class ProductBloc extends Bloc<ProductEvent, ProductState> {
  final ProductRepository productRepository;

  ProductBloc(this.productRepository) : super(ProductInitial()) {
    on<LoadProducts>((event, emit) async {
      emit(ProductLoading());
      try {
        final products = await productRepository.getAllProducts(
          search: event.search,
          categoryId: event.categoryId,
          minPrice: event.minPrice,
          maxPrice: event.maxPrice,
        );
        emit(ProductsLoaded(products));
      } catch (e) {
        emit(ProductError('Erreur lors du chargement des produits'));
      }
    });

    on<LoadProductDetails>((event, emit) async {
      emit(ProductLoading());
      try {
        final product = await productRepository.getProductById(event.productId);
        emit(ProductDetailsLoaded(product));
      } catch (e) {
        emit(ProductError('Erreur lors du chargement du produit'));
      }
    });
  }
}
