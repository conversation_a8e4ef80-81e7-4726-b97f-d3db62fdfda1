import 'package:flutter/material.dart';

class NotificationFilter extends StatelessWidget {
  final String? selectedStatus;
  final Function(String?) onFilterChanged;

  const NotificationFilter({
    super.key,
    this.selectedStatus,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey, width: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filtrer les notifications',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          // Status Filter
          Row(
            children: [
              const Text(
                'Statut: ',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Wrap(
                  spacing: 8,
                  children: [
                    _buildFilterChip(
                      label: 'Toutes',
                      isSelected: selectedStatus == null,
                      onTap: () => onFilterChanged(null),
                    ),
                    _buildFilterChip(
                      label: 'Non lues',
                      isSelected: selectedStatus == 'UNREAD',
                      onTap: () => onFilterChanged('UNREAD'),
                    ),
                    _buildFilterChip(
                      label: 'Lues',
                      isSelected: selectedStatus == 'READ',
                      onTap: () => onFilterChanged('READ'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.shade300,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : Colors.grey.shade700,
          ),
        ),
      ),
    );
  }
}
