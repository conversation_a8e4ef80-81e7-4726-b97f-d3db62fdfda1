import 'package:flutter/material.dart';
import '../../data/models/notification_model.dart';

class NotificationItem extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;

  const NotificationItem({
    super.key,
    required this.notification,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: notification.isUnread ? Colors.blue.shade50 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notification.isUnread
              ? Colors.blue.shade200
              : Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with type and time
              Row(
                children: [
                  _buildTypeIcon(),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getTypeDisplayName(),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: _getTypeColor(),
                      ),
                    ),
                  ),
                  if (notification.isUnread)
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.blue,
                        shape: BoxShape.circle,
                      ),
                    ),
                  const SizedBox(width: 8),
                  Text(
                    _formatTime(notification.createdAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Title
              Text(
                notification.title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight:
                      notification.isUnread ? FontWeight.w600 : FontWeight.w500,
                  color: Colors.grey[900],
                ),
              ),
              const SizedBox(height: 8),

              // Message
              Text(
                notification.message,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  height: 1.4,
                ),
              ),

              // Additional data if available
              if (notification.data != null && notification.data!.isNotEmpty)
                ..._buildAdditionalInfo(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeIcon() {
    IconData iconData;
    Color iconColor;

    switch (notification.type.toLowerCase()) {
      case 'prescription':
        iconData = Icons.description;
        iconColor = Colors.blue;
        break;
      case 'package':
        iconData = Icons.local_shipping;
        iconColor = Colors.green;
        break;
      case 'order':
        iconData = Icons.shopping_bag;
        iconColor = Colors.orange;
        break;
      case 'system':
        iconData = Icons.info;
        iconColor = Colors.purple;
        break;
      default:
        iconData = Icons.notifications;
        iconColor = Colors.grey;
    }

    return Icon(
      iconData,
      size: 16,
      color: iconColor,
    );
  }

  String _getTypeDisplayName() {
    switch (notification.type.toLowerCase()) {
      case 'prescription':
        return 'Ordonnance';
      case 'package':
        return 'Colis';
      case 'order':
        return 'Commande';
      case 'system':
        return 'Système';
      default:
        return 'Notification';
    }
  }

  Color _getTypeColor() {
    switch (notification.type.toLowerCase()) {
      case 'prescription':
        return Colors.blue;
      case 'package':
        return Colors.green;
      case 'order':
        return Colors.orange;
      case 'system':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'À l\'instant';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}j';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }

  List<Widget> _buildAdditionalInfo() {
    final data = notification.data!;
    final widgets = <Widget>[];

    // Only show package ID, not prescription ID
    if (data.containsKey('packageId')) {
      widgets.add(const SizedBox(height: 8));
      widgets.add(
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.green.shade100,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            'Colis: ${data['packageId']}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.green.shade700,
            ),
          ),
        ),
      );
    }

    return widgets;
  }
}
