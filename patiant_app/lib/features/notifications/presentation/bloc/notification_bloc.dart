import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/notification_repository.dart';
import '../../data/models/notification_model.dart';
import 'notification_event.dart';
import 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationRepository repository;

  // Keep track of current filters and pagination
  String? _currentStatus;
  String? _currentType;
  int _currentPage = 1;
  List<NotificationModel> _allNotifications = [];

  NotificationBloc(this.repository) : super(NotificationInitial()) {
    on<LoadNotifications>(_onLoadNotifications);
    on<LoadMoreNotifications>(_onLoadMoreNotifications);
    on<MarkNotificationAsRead>(_onMarkNotificationAsRead);
    on<UpdateFcmToken>(_onUpdateFcmToken);
    on<LoadUnreadCount>(_onLoadUnreadCount);
    on<RefreshNotifications>(_onRefreshNotifications);
    on<FilterNotifications>(_onFilterNotifications);
  }

  Future<void> _onLoadNotifications(
    LoadNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      print('🔔 Loading notifications...');
      emit(NotificationLoading());

      _currentStatus = event.status;
      _currentType = event.type;
      _currentPage = event.page;

      final result = await repository.getUserNotifications(
        status: event.status,
        type: event.type,
        page: event.page,
        limit: event.limit,
      );

      _allNotifications = result['notifications'];

      print(
          '🔔 Loaded ${_allNotifications.length} notifications, unread: ${result['unreadCount']}');

      emit(NotificationLoaded(
        notifications: _allNotifications,
        total: result['total'],
        unreadCount: result['unreadCount'],
        currentPage: result['currentPage'],
        totalPages: result['totalPages'],
        hasMore: result['currentPage'] < result['totalPages'],
      ));
    } catch (e) {
      print('❌ Error loading notifications: $e');
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onLoadMoreNotifications(
    LoadMoreNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is NotificationLoaded && currentState.hasMore) {
        emit(NotificationLoadingMore(
          currentNotifications: currentState.notifications,
          unreadCount: currentState.unreadCount,
        ));

        final nextPage = currentState.currentPage + 1;
        final result = await repository.getUserNotifications(
          status: event.status ?? _currentStatus,
          type: event.type ?? _currentType,
          page: nextPage,
          limit: 20,
        );

        final newNotifications =
            result['notifications'] as List<NotificationModel>;
        _allNotifications.addAll(newNotifications);
        _currentPage = nextPage;

        emit(NotificationLoaded(
          notifications: _allNotifications,
          total: result['total'],
          unreadCount: result['unreadCount'],
          currentPage: result['currentPage'],
          totalPages: result['totalPages'],
          hasMore: result['currentPage'] < result['totalPages'],
        ));
      }
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onMarkNotificationAsRead(
    MarkNotificationAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final updatedNotification =
          await repository.markAsRead(event.notificationId);

      // Update the notification in our local list
      final index =
          _allNotifications.indexWhere((n) => n.id == event.notificationId);
      if (index != -1) {
        _allNotifications[index] = updatedNotification;
      }

      // Calculate new unread count
      final newUnreadCount = _allNotifications.where((n) => n.isUnread).length;

      // Emit updated state immediately to keep notifications visible
      if (state is NotificationLoaded) {
        final currentState = state as NotificationLoaded;
        emit(currentState.copyWith(
          notifications: List.from(_allNotifications),
          unreadCount: newUnreadCount,
        ));
      }

      // Also emit success state for any listeners that need it
      emit(NotificationMarkAsReadSuccess(
        notification: updatedNotification,
        newUnreadCount: newUnreadCount,
      ));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onUpdateFcmToken(
    UpdateFcmToken event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await repository.updateFcmToken(event.fcmToken);
      emit(FcmTokenUpdateSuccess());
    } catch (e) {
      emit(FcmTokenUpdateError(e.toString()));
    }
  }

  Future<void> _onLoadUnreadCount(
    LoadUnreadCount event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final count = await repository.getUnreadCount();
      emit(UnreadCountLoaded(count));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onRefreshNotifications(
    RefreshNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _currentPage = 1;
      _allNotifications.clear();

      add(LoadNotifications(
        status: _currentStatus,
        type: _currentType,
        page: 1,
        limit: 20,
      ));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onFilterNotifications(
    FilterNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _currentStatus = event.status;
      _currentType = event.type;
      _currentPage = 1;
      _allNotifications.clear();

      add(LoadNotifications(
        status: event.status,
        type: event.type,
        page: 1,
        limit: 20,
      ));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }
}
