import 'package:equatable/equatable.dart';

abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object?> get props => [];
}

class LoadNotifications extends NotificationEvent {
  final String? status;
  final String? type;
  final int page;
  final int limit;

  const LoadNotifications({
    this.status,
    this.type,
    this.page = 1,
    this.limit = 20,
  });

  @override
  List<Object?> get props => [status, type, page, limit];
}

class LoadMoreNotifications extends NotificationEvent {
  final String? status;
  final String? type;

  const LoadMoreNotifications({
    this.status,
    this.type,
  });

  @override
  List<Object?> get props => [status, type];
}

class MarkNotificationAsRead extends NotificationEvent {
  final String notificationId;

  const MarkNotificationAsRead(this.notificationId);

  @override
  List<Object> get props => [notificationId];
}

class UpdateFcmToken extends NotificationEvent {
  final String fcmToken;

  const UpdateFcmToken(this.fcmToken);

  @override
  List<Object> get props => [fcmToken];
}

class LoadUnreadCount extends NotificationEvent {
  const LoadUnreadCount();
}

class RefreshNotifications extends NotificationEvent {
  const RefreshNotifications();
}

class FilterNotifications extends NotificationEvent {
  final String? status;
  final String? type;

  const FilterNotifications({
    this.status,
    this.type,
  });

  @override
  List<Object?> get props => [status, type];
}
