import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import '../bloc/notification_bloc.dart';
import '../bloc/notification_event.dart';
import '../bloc/notification_state.dart';
import '../widgets/notification_item.dart';
import '../widgets/notification_filter.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  final ScrollController _scrollController = ScrollController();
  String? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
    _setupScrollListener();
  }

  void _loadNotifications() {
    context.read<NotificationBloc>().add(const LoadNotifications());
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        final currentState = context.read<NotificationBloc>().state;
        if (currentState is NotificationLoaded && currentState.hasMore) {
          context.read<NotificationBloc>().add(LoadMoreNotifications(
                status: _selectedStatus,
              ));
        }
      }
    });
  }

  void _onFilterChanged(String? status) {
    setState(() {
      _selectedStatus = status;
    });

    context.read<NotificationBloc>().add(LoadNotifications(
          status: status,
        ));
  }

  void _onRefresh() {
    context.read<NotificationBloc>().add(const RefreshNotifications());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, profileState) {
        String? profilePhoto;
        String firstName = '';
        String lastName = '';

        if (profileState is ProfileLoaded) {
          profilePhoto = profileState.profilePhoto;
          firstName = profileState.firstName;
          lastName = profileState.lastName;
        }

        return BlocBuilder<NotificationBloc, NotificationState>(
          builder: (context, notificationState) {
            int unreadCount = 0;
            if (notificationState is NotificationLoaded) {
              unreadCount = notificationState.unreadCount;
            } else if (notificationState is UnreadCountLoaded) {
              unreadCount = notificationState.count;
            }

            return Scaffold(
              backgroundColor: const Color(0xFFF6F9FC),
              appBar: CustomAppBar(
                logoPath: 'assets/logo.png',
                profilePhoto: profilePhoto,
                firstName: firstName,
                lastName: lastName,
                notificationCount: unreadCount > 0 ? unreadCount : null,
                onNotificationTap: () {
                  // Already on notification screen
                },
                onProfileTap: () {
                  Navigator.pushReplacementNamed(context, '/profile');
                },
              ),
              body: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.arrow_back),
                          onPressed: () => Navigator.pop(context),
                        ),
                        const Expanded(
                          child: Text(
                            'Notifications',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: _onRefresh,
                        ),
                      ],
                    ),
                  ),

                  // Filter
                  NotificationFilter(
                    selectedStatus: _selectedStatus,
                    onFilterChanged: _onFilterChanged,
                  ),

                  // Notifications List
                  Expanded(
                    child: BlocListener<NotificationBloc, NotificationState>(
                      listener: (context, state) {
                        if (state is NotificationMarkAsReadSuccess) {
                          // Automatically reload notifications after marking as read
                          context
                              .read<NotificationBloc>()
                              .add(const LoadNotifications());
                        }
                      },
                      child: BlocBuilder<NotificationBloc, NotificationState>(
                        builder: (context, state) {
                          if (state is NotificationLoading) {
                            return const Center(
                                child: CircularProgressIndicator());
                          }

                          if (state is NotificationError) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    size: 64,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Erreur lors du chargement',
                                    style: TextStyle(
                                      fontSize: 18,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    state.message,
                                    style: TextStyle(
                                      color: Colors.grey[500],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 16),
                                  ElevatedButton(
                                    onPressed: _onRefresh,
                                    child: const Text('Réessayer'),
                                  ),
                                ],
                              ),
                            );
                          }

                          if (state is NotificationLoaded) {
                            if (state.notifications.isEmpty) {
                              return Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.notifications_none,
                                      size: 64,
                                      color: Colors.grey[400],
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'Aucune notification',
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Vous n\'avez pas encore reçu de notifications',
                                      style: TextStyle(
                                        color: Colors.grey[500],
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }

                            return RefreshIndicator(
                              onRefresh: () async => _onRefresh(),
                              child: ListView.builder(
                                controller: _scrollController,
                                padding: const EdgeInsets.all(16),
                                itemCount: state.notifications.length +
                                    (state.hasMore ? 1 : 0),
                                itemBuilder: (context, index) {
                                  if (index == state.notifications.length) {
                                    return const Center(
                                      child: Padding(
                                        padding: EdgeInsets.all(16),
                                        child: CircularProgressIndicator(),
                                      ),
                                    );
                                  }

                                  final notification =
                                      state.notifications[index];
                                  return NotificationItem(
                                    notification: notification,
                                    onTap: () {
                                      if (notification.isUnread) {
                                        // Just mark as read without changing filter
                                        context.read<NotificationBloc>().add(
                                              MarkNotificationAsRead(
                                                  notification.id),
                                            );
                                      }
                                    },
                                  );
                                },
                              ),
                            );
                          }

                          return const SizedBox.shrink();
                        },
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
