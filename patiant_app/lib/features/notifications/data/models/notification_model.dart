class NotificationModel {
  final String id;
  final String recipientId;
  final String? senderId;
  final String title;
  final String message;
  final String type;
  final String status;
  final Map<String, dynamic>? data;
  final bool fcmSent;
  final String? fcmMessageId;
  final DateTime? readAt;
  final DateTime? archivedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  NotificationModel({
    required this.id,
    required this.recipientId,
    this.senderId,
    required this.title,
    required this.message,
    required this.type,
    required this.status,
    this.data,
    required this.fcmSent,
    this.fcmMessageId,
    this.readAt,
    this.archivedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      recipientId: json['recipientId'] ?? '',
      senderId: json['senderId'],
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: json['type'] ?? '',
      status: json['status'] ?? 'UNREAD',
      data: json['data'],
      fcmSent: json['fcmSent'] ?? false,
      fcmMessageId: json['fcmMessageId'],
      readAt: json['readAt'] != null ? DateTime.parse(json['readAt']) : null,
      archivedAt: json['archivedAt'] != null ? DateTime.parse(json['archivedAt']) : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'recipientId': recipientId,
      'senderId': senderId,
      'title': title,
      'message': message,
      'type': type,
      'status': status,
      'data': data,
      'fcmSent': fcmSent,
      'fcmMessageId': fcmMessageId,
      'readAt': readAt?.toIso8601String(),
      'archivedAt': archivedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  bool get isUnread => status == 'UNREAD';
  bool get isRead => status == 'READ';

  NotificationModel copyWith({
    String? id,
    String? recipientId,
    String? senderId,
    String? title,
    String? message,
    String? type,
    String? status,
    Map<String, dynamic>? data,
    bool? fcmSent,
    String? fcmMessageId,
    DateTime? readAt,
    DateTime? archivedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      recipientId: recipientId ?? this.recipientId,
      senderId: senderId ?? this.senderId,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      status: status ?? this.status,
      data: data ?? this.data,
      fcmSent: fcmSent ?? this.fcmSent,
      fcmMessageId: fcmMessageId ?? this.fcmMessageId,
      readAt: readAt ?? this.readAt,
      archivedAt: archivedAt ?? this.archivedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
