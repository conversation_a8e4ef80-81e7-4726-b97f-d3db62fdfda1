import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FirebaseMessagingService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static String? _fcmToken;
  static Function(String)? _onTokenRefresh;
  static Function(Map<String, dynamic>)? _onMessageReceived;

  // Initialize Firebase Messaging
  static Future<void> initialize({
    Function(String)? onTokenRefresh,
    Function(Map<String, dynamic>)? onMessageReceived,
  }) async {
    _onTokenRefresh = onTokenRefresh;
    _onMessageReceived = onMessageReceived;

    // Request permission for notifications
    await _requestPermission();

    // Initialize local notifications
    await _initializeLocalNotifications();

    // Get initial FCM token
    await _getToken();

    // Listen for token refresh
    _firebaseMessaging.onTokenRefresh.listen((token) {
      _fcmToken = token;
      _saveTokenLocally(token);
      _onTokenRefresh?.call(token);
    });

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background/terminated
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Check if app was opened from a notification
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
  }

  // Request notification permissions
  static Future<void> _requestPermission() async {
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    print('Notification permission status: ${settings.authorizationStatus}');
  }

  // Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onLocalNotificationTap,
    );
  }

  // Get FCM token
  static Future<String?> _getToken() async {
    try {
      // For iOS simulator, try to get FCM token directly without APNS check
      // This allows testing in simulator environment
      _fcmToken = await _firebaseMessaging.getToken();
      if (_fcmToken != null) {
        await _saveTokenLocally(_fcmToken!);
        _onTokenRefresh?.call(_fcmToken!);
        print(
            'FCM token obtained successfully: ${_fcmToken!.substring(0, 20)}...');
      } else {
        print('FCM token is null');
      }
      return _fcmToken;
    } catch (e) {
      print('Error getting FCM token: $e');
      // For iOS simulator, create a mock token for testing
      if (Platform.isIOS && e.toString().contains('apns-token-not-set')) {
        print('Creating mock FCM token for iOS simulator testing');
        _fcmToken = 'mock_fcm_token_${DateTime.now().millisecondsSinceEpoch}';
        await _saveTokenLocally(_fcmToken!);
        _onTokenRefresh?.call(_fcmToken!);
        return _fcmToken;
      }
      return null;
    }
  }

  // Save token locally
  static Future<void> _saveTokenLocally(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('fcm_token', token);
  }

  // Get saved token
  static Future<String?> getSavedToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('fcm_token');
  }

  // Get current token
  static String? getCurrentToken() => _fcmToken;

  // Get FCM token (public method)
  static Future<String?> getToken() async {
    if (_fcmToken != null) {
      return _fcmToken;
    }

    // Try multiple times with delays for iOS APNS token
    for (int attempt = 1; attempt <= 3; attempt++) {
      final token = await _getToken();
      if (token != null) {
        return token;
      }

      if (attempt < 3) {
        print(
            'FCM token attempt $attempt failed, retrying in ${attempt * 2} seconds...');
        await Future.delayed(Duration(seconds: attempt * 2));
      }
    }

    print('Failed to get FCM token after 3 attempts');
    return null;
  }

  // Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('Received foreground message: ${message.messageId}');

    // Show local notification
    await _showLocalNotification(message);

    // Notify app about the message
    _onMessageReceived?.call(message.data);
  }

  // Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('Received background message: ${message.messageId}');
    // Background messages are automatically shown by the system
  }

  // Handle notification tap
  static void _handleNotificationTap(RemoteMessage message) {
    print('Notification tapped: ${message.messageId}');
    _onMessageReceived?.call(message.data);
  }

  // Handle local notification tap
  static void _onLocalNotificationTap(NotificationResponse response) {
    if (response.payload != null) {
      final data = jsonDecode(response.payload!);
      _onMessageReceived?.call(data);
    }
  }

  // Show local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    const androidDetails = AndroidNotificationDetails(
      'patient_notifications',
      'Patient Notifications',
      channelDescription: 'Notifications for patient app',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'Nouvelle notification',
      message.notification?.body ?? '',
      details,
      payload: jsonEncode(message.data),
    );
  }

  // Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
  }

  // Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
  }

  // Clear all notifications
  static Future<void> clearAllNotifications() async {
    await _localNotifications.cancelAll();
  }
}

// Top-level function for background message handling
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  print('Background message received: ${message.messageId}');
}
