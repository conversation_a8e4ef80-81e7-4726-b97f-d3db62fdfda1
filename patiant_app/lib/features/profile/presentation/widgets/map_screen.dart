import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

class MapScreen extends StatelessWidget {
  final LatLng location;
  final double zoomLevel;
  final bool showAppBar;
  final bool interactive; // 👈 gesture toggle parameter

  const MapScreen({
    Key? key,
    required this.location,
    this.zoomLevel = 10.0,
    this.showAppBar = false,
    this.interactive = false, // default false for read-only map
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: showAppBar ? AppBar(title: const Text('Map View')) : null,
      body: FlutterMap(
        mapController: MapController(),
        options: MapOptions(
          initialCenter: location,
          initialZoom: zoomLevel,
          interactionOptions: InteractionOptions(
            flags: interactive
                ? InteractiveFlag.all
                : InteractiveFlag.none, // 👈 Here is the fix!
          ),
        ),
        children: [
          TileLayer(
            urlTemplate:
                "http://mt0.google.com/vt/lyrs=m&hl=en&x={x}&y={y}&z={z}",
            subdomains: ['a', 'b', 'c'],
          ),
          MarkerLayer(
            markers: [
              Marker(
                point: location,
                width: 40,
                height: 40,
                child: const Icon(
                  Icons.location_pin,
                  size: 40,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
