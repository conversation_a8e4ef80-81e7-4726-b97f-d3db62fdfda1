import 'dart:io';

import 'package:dio/dio.dart';
import 'package:patiant_app/core/network/api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

class ProfileRepository {
  final ApiService _apiService = ApiService();

  // ✅ Fetch User Profile from Backend
  Future<Map<String, dynamic>?> fetchUserProfile() async {
    try {
      Response response =
          await _apiService.get('/user/getProfile', requiresAuth: true);

      if (response.statusCode == 200) {
        final profileData = response.data['data'];

        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.setString("id", profileData["id"]);
        prefs.setString("firstName", profileData["firstName"]);
        prefs.setString("lastName", profileData["lastName"]);
        prefs.setString("phoneNumber", profileData["phoneNumber"]);
        prefs.setString("email", profileData["email"]);
        prefs.setBool("isTwoFactorEnabled", profileData["isTwoFactorEnabled"]);
        prefs.setString("profilePhoto", profileData["profilePhoto"] ?? '');

        return profileData;
      }
      return null;
    } catch (e) {
      print("⚠ Error fetching profile: $e");
      return null;
    }
  }

  // ✅ Upload Profile Photo
  Future<String?> uploadProfilePhoto(File photo) async {
    try {
      FormData formData = FormData.fromMap({
        "file": await MultipartFile.fromFile(photo.path),
      });

      Response response = await _apiService.post(
        '/user/uploadProfilePhoto',
        formData,
        requiresAuth: true,
      );

      if (response.statusCode == 201) {
        return response.data['profilePhoto'];
      }
      return null;
    } catch (e) {
      print("⚠ Error uploading profile photo: $e");
      return null;
    }
  }

// ✅ Update User Profile
  Future<bool> updateUserProfile({
    required String firstName,
    required String lastName,
    required String email,
    required String phoneNumber,
    String? profilePhoto,
  }) async {
    try {
      final Map<String, dynamic> data = {
        "user": {
          "email": email,
          "phoneNumber": phoneNumber,
          if (profilePhoto != null) "profilePhoto": profilePhoto,
        },
        "patient": {
          "firstName": firstName,
          "lastName": lastName,
        }
      };

      Response response = await _apiService.put(
        '/user/updateProfile',
        data,
        requiresAuth: true,
      );

      return response.statusCode == 201;
    } catch (e) {
      print("⚠ Error updating profile: $e");
      return false;
    }
  }

  // ✅ Enable Two-Factor Authentication
  Future<bool> enableTwoFactor() async {
    try {
      Response response =
          await _apiService.post('/user/enable-2fa', {}, requiresAuth: true);
      return response.statusCode == 200;
    } catch (e) {
      print("⚠ Error enabling 2FA: $e");
      return false;
    }
  }

  // ✅ Disable Two-Factor Authentication
  Future<bool> disableTwoFactor() async {
    try {
      Response response =
          await _apiService.post('/user/disable-2fa', {}, requiresAuth: true);
      return response.statusCode == 200;
    } catch (e) {
      print("⚠ Error disabling 2FA: $e");
      return false;
    }
  }
}
