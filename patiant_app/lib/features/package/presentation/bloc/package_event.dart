abstract class PackageEvent {}

class LoadPackages extends PackageEvent {
  final String? date;
  final String? deliveryPeriod;
  final String? status;
  final String? searchQuery;
  LoadPackages({this.date, this.deliveryPeriod, this.status, this.searchQuery});
}

class LoadPackageDetails extends PackageEvent {
  final String packageId;
  LoadPackageDetails(this.packageId);
}

class FilterPackages extends PackageEvent {
  final String? searchQuery;
  final String? status;

  FilterPackages({this.searchQuery, this.status});
}

class LoadPackageCount extends PackageEvent {}

class LoadTotalPackagePrice extends PackageEvent {}
