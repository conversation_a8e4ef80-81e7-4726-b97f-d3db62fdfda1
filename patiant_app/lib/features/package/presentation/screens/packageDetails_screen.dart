import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:latlong2/latlong.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/package/presentation/bloc/package_bloc.dart';
import 'package:patiant_app/features/package/presentation/bloc/package_event.dart';
import 'package:patiant_app/features/package/presentation/bloc/package_state.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_event.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/profile/presentation/widgets/map_screen.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';

class PackageDetailsScreen extends StatelessWidget {
  final String packageId;

  const PackageDetailsScreen({Key? key, required this.packageId})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
            create: (context) =>
                ProfileBloc(context.read())..add(LoadProfile())),
        BlocProvider(
            create: (context) => PackageBloc(context.read())
              ..add(LoadPackageDetails(packageId))),
        BlocProvider.value(value: context.read<NotificationBloc>()),
      ],
      child: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, profileState) {
          String? profilePhoto;
          String firstName = '';
          String lastName = '';

          if (profileState is ProfileLoaded) {
            profilePhoto = profileState.profilePhoto;
            firstName = profileState.firstName;
            lastName = profileState.lastName;
          }

          return BlocBuilder<NotificationBloc, NotificationState>(
            builder: (context, notificationState) {
              int unreadCount = 0;
              if (notificationState is NotificationLoaded) {
                unreadCount = notificationState.unreadCount;
              } else if (notificationState is UnreadCountLoaded) {
                unreadCount = notificationState.count;
              }

              return Scaffold(
                backgroundColor: Colors.white,
                appBar: CustomAppBar(
                  logoPath: 'assets/logo.png',
                  notificationCount: unreadCount > 0 ? unreadCount : null,
                  onNotificationTap: () {
                    Navigator.pushNamed(context, '/notifications');
                  },
                  onProfileTap: () {
                    Navigator.pushReplacementNamed(context, '/profile');
                  },
                  profilePhoto: profilePhoto,
                  firstName: firstName,
                  lastName: lastName,
                ),
                bottomNavigationBar: CustomNavBar(
                  selectedIndex: 1,
                  onTap: (index) {
                    if (index == 0) {
                      Navigator.pushReplacementNamed(context, '/home');
                    } else if (index == 2) {
                      Navigator.pushReplacementNamed(context, '/messages');
                    } else if (index == 3) {
                      Navigator.pushReplacementNamed(context, '/profile');
                    }
                  },
                ),
                body: BlocBuilder<PackageBloc, PackageState>(
                  builder: (context, state) {
                    if (state is PackageLoading) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (state is PackageDetailsLoaded) {
                      final package = state.packageDetails;
                      final householdName = state.householdName;
                      final isHousehold = package['householdReceiver'] != null;
                      final pharmacyName =
                          state.pharmacyName ?? package['pharmacyId'];

                      double latitude = package['originAddress']['lat'] ?? 0.0;
                      double longitude =
                          package['originAddress']['long'] ?? 0.0;
                      LatLng packageLocation = LatLng(latitude, longitude);

                      return SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.arrow_back,
                                        color: Colors.black),
                                    onPressed: () => Navigator.pop(context),
                                  ),
                                  Expanded(
                                    child: Center(
                                      child: Text(
                                        package['isPicking'] == true
                                            ? "Détail du cueillette"
                                            : "Détail du colis",
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 20),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 40),
                                ],
                              ),
                              const SizedBox(height: 20),

                              // Blue header
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF3167AC),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Livraison - jour - ${package['deliveryTime']}",
                                          style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          "Pharmacie : " + pharmacyName,
                                          style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 14),
                                        ),
                                      ],
                                    ),
                                    const Icon(Icons.local_shipping,
                                        color: Colors.white, size: 30),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 20),

                              // Details Card
                              Card(
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10)),
                                elevation: 2,
                                child: Padding(
                                  padding: const EdgeInsets.all(15),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          if (isHousehold &&
                                              householdName != null)
                                            Text(
                                              'Foyer: $householdName',
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                  color: Colors.deepPurple),
                                            )
                                          else
                                            const SizedBox.shrink(),
                                          Row(
                                            children: [
                                              const Icon(Icons.access_time,
                                                  size: 18, color: Colors.blue),
                                              const SizedBox(width: 5),
                                              Text(
                                                package['deliveryTime'],
                                                style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 10),
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              _buildCircle(Colors.blue),
                                              _buildDashedLine(),
                                              _buildCircle(Colors.green),
                                            ],
                                          ),
                                          const SizedBox(width: 10),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                const Text("Pharmacie",
                                                    style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color: Colors.blue)),
                                                Text(
                                                  pharmacyName,
                                                  style: const TextStyle(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500),
                                                ),
                                                const SizedBox(height: 10),
                                                const Text(
                                                    "Adresse de livraison",
                                                    style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color: Colors.blue)),
                                                Text(
                                                    package['originAddress']
                                                        ['address'],
                                                    style: const TextStyle(
                                                        fontSize: 16)),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(12),
                                        child: SizedBox(
                                          height: 150,
                                          child: MapScreen(
                                            location: packageLocation,
                                            interactive: false,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: [
                                          Column(
                                            children: [
                                              _buildCircle(Colors.blue),
                                              const SizedBox(height: 6),
                                              const Text("Pharmacie"),
                                            ],
                                          ),
                                          _buildHorizontalLine(),
                                          Column(
                                            children: [
                                              _buildCircle(Colors.blue),
                                              const SizedBox(height: 6),
                                              const Text("En cours"),
                                            ],
                                          ),
                                          _buildHorizontalLine(),
                                          Column(
                                            children: [
                                              _buildCircle(Colors.grey),
                                              const SizedBox(height: 6),
                                              const Text("Destination"),
                                            ],
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 12),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 8),
                                        decoration: BoxDecoration(
                                          color: _getStatusColor(
                                                  package['status'] ?? '')
                                              .withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Center(
                                          child: Text(
                                            "Colis ${package['status']}",
                                            style: TextStyle(
                                                color: _getStatusColor(
                                                    package['status'] ?? ''),
                                                fontWeight: FontWeight.bold),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              const SizedBox(height: 20),
                              _buildPaymentDetailsCard(package),
                            ],
                          ),
                        ),
                      );
                    } else {
                      return const Center(child: Text("Aucun détail trouvé"));
                    }
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildCircle(Color color) {
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(color: color, shape: BoxShape.circle),
    );
  }

  Widget _buildDashedLine() {
    return Column(
      children: List.generate(
        3,
        (index) => Container(
          width: 2,
          height: 6,
          margin: const EdgeInsets.symmetric(vertical: 2),
          color: Colors.grey.shade400,
        ),
      ),
    );
  }

  Widget _buildHorizontalLine() {
    return Container(width: 40, height: 2, color: Colors.blue);
  }

  Widget _buildPaymentDetailsCard(Map<String, dynamic> package) {
    final isHousehold = package['householdReceiver'] != null;

    final paymentType = isHousehold
        ? (package['householdReceiver']?['patients']?[0]?['paymentType'] ??
            "Non spécifiée")
        : (package['patientReceiver']?['paymentType'] ?? "Non spécifiée");

    final signatureType = isHousehold
        ? (package['householdReceiver']?['patients']?[0]?['signatureType'] ??
            "Non spécifiée")
        : (package['patientReceiver']?['signatureType'] ?? "Non spécifiée");

    final authorizedType = isHousehold
        ? (package['householdReceiver']?['patients']?[0]['authorizedPerson']
                ?['type'] ??
            "Non spécifié")
        : (package['patientReceiver']?['authorizedPerson']?['type'] ??
            "Non spécifié");

    final authorizedName = isHousehold
        ? (package['householdReceiver']?['patients']?[0]['authorizedPerson']
                ?['name'] ??
            "Non spécifié")
        : (package['patientReceiver']?['authorizedPerson']?['name'] ??
            "Non spécifié");

    final price = isHousehold
        ? (package['householdReceiver']?['patients']?[0]?['packagePrice'] ??
            0.0)
        : (package['patientReceiver']?['packagePrice'] ?? 0.0);

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text("Détail du paiement",
                style: TextStyle(
                    fontSize: 18,
                    color: Colors.blue,
                    fontWeight: FontWeight.bold)),
            const SizedBox(height: 10),
            _buildPaymentDetailRow("Méthode de paiement", paymentType),
            _buildPaymentDetailRow("Confirmation", signatureType),
            const SizedBox(height: 10),
            const Text("Personnes autorisées à recevoir les livraisons",
                style:
                    TextStyle(fontWeight: FontWeight.bold, color: Colors.blue)),
            _buildPaymentDetailRow("Type", authorizedType),
            _buildPaymentDetailRow("Nom", authorizedName),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text("Total prix",
                    style:
                        TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                Text(
                  "${price.toStringAsFixed(3)}\$",
                  style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                      color: Colors.blue),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label,
              style:
                  const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case "Validé":
        return Colors.green;
      case "Annulé":
        return Colors.red;
      case "En cours":
      case "En préparation":
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}
