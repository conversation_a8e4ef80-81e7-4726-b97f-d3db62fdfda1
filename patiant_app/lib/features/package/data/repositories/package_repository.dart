import 'package:dio/dio.dart';
import 'package:patiant_app/core/network/api_service.dart';

class PackageRepository {
  final ApiService _apiService = ApiService();

  // ✅ Fetch packages with optional date and deliveryPeriod (AM/PM)
  Future<List<Map<String, dynamic>>> fetchPackages({
    String? date,
    String? deliveryPeriod, // <-- Add delivery period
  }) async {
    try {
      final String dateParam =
          date ?? DateTime.now().toIso8601String().substring(0, 10);

      Response response = await _apiService.get(
        '/package/getAllPackages',
        requiresAuth: true,
        queryParams: {
          'date': dateParam,
          if (deliveryPeriod != null && deliveryPeriod.isNotEmpty)
            'deliveryPeriod':
                deliveryPeriod, // ✅ Add delivery period in request
        },
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      }
      return [];
    } catch (e) {
      print("⚠ Error fetching packages: $e");
      return [];
    }
  }

  // ✅ Fetch package details by packageId
  Future<Map<String, dynamic>?> fetchPackageDetails(String packageId) async {
    try {
      Response response = await _apiService.get(
        '/package/getPackageDetails/$packageId',
        requiresAuth: true,
      );

      if (response.statusCode == 200) {
        return response.data['data'];
      }
      return null;
    } catch (e) {
      print("⚠ Error fetching package details: $e");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> fetchFilteredPackages({
    String? searchQuery,
    String? status,
    String? date,
    String? period, // <-- add this
  }) async {
    try {
      Response response = await _apiService.get(
        '/package/filter',
        requiresAuth: true,
        queryParams: {
          if (searchQuery != null && searchQuery.isNotEmpty)
            'searchQuery': searchQuery,
          if (status != null && status.isNotEmpty) 'status': status,
          if (date != null && date.isNotEmpty) 'date': date,
          if (period != null && period.isNotEmpty) 'period': period, // ✅
        },
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      }
      return [];
    } catch (e) {
      print("⚠ Error fetching filtered packages: $e");
      return [];
    }
  }

  // ✅ Fetch total package count
  Future<int> fetchPackageCount() async {
    try {
      Response response = await _apiService.get(
        '/package/count',
        requiresAuth: true,
      );

      if (response.statusCode == 200) {
        return response.data['count'];
      }
      return 0;
    } catch (e) {
      print("⚠ Error fetching package count: $e");
      return 0;
    }
  }

  Future<double> fetchTotalPackagePrice() async {
    try {
      Response response = await _apiService.get(
        '/package/totalPrice',
        requiresAuth: true,
      );

      if (response.statusCode == 200) {
        return (response.data['totalPrice'] ?? 0).toDouble();
      }
      return 0;
    } catch (e) {
      print("⚠ Error fetching total package price: $e");
      return 0;
    }
  }

  Future<String?> fetchHouseholdName(String householdId) async {
    try {
      final response = await _apiService.get(
        '/household/getHouseHoldDetails/$householdId',
        requiresAuth: true,
      );

      if (response.statusCode == 200) {
        return response.data['data']
            ['name']; // assuming household has a 'name' field
      }
      return null;
    } catch (e) {
      print("⚠ Error fetching household name: $e");
      return null;
    }
  }

  Future<String?> fetchPharmacyName(String pharmacyId) async {
    try {
      final response = await _apiService.get(
        '/pharmacy/getPharmacyInfo/$pharmacyId',
        requiresAuth: true,
      );

      if (response.statusCode == 200) {
        return response.data['data']['pharmacyName'];
      }
      return null;
    } catch (e) {
      print("⚠ Error fetching pharmacy name: $e");
      return null;
    }
  }
}
