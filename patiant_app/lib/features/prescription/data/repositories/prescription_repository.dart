import 'dart:io';
import 'package:dio/dio.dart';
import 'package:patiant_app/core/network/api_service.dart';

class PrescriptionRepository {
  final ApiService _api = ApiService();

  Future<Map<String, dynamic>> getPrescriptions(
      {String? date, String? note}) async {
    final response = await _api.get(
      "/prescriptions/getAll",
      requiresAuth: true,
      queryParams: {
        if (date != null) "date": date,
        if (note != null && note.isNotEmpty) "note": note,
      },
    );
    return {
      "prescriptions": response.data['prescriptions'],
      "total": response.data['total'],
    };
  }

  Future<Map<String, dynamic>> getPrescriptionById(String id) async {
    final response = await _api.get(
      "/prescriptions/get/$id",
      requiresAuth: true,
    );
    return response.data['prescription'];
  }

  Future<List<String>> uploadFiles(List<File> files) async {
    List<String> paths = [];

    for (File file in files) {
      final fileName = file.path.split('/').last;
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(file.path, filename: fileName),
      });

      final response = await _api.post(
        "/prescriptions/uploadFile",
        formData,
        requiresAuth: true,
      );

      paths.add(response.data['filePath']);
    }

    return paths;
  }

  Future<void> createPrescription(List<String> storagePaths,
      {String? note, String? issueDate}) async {
    await _api.post(
      "/prescriptions/create",
      {
        "storagePaths": storagePaths,
        if (note != null) "note": note,
        if (issueDate != null) "issueDate": issueDate,
      },
      requiresAuth: true,
    );
  }

  Future<void> updatePrescription(
    String prescriptionId,
    String? note,
    List<String> storagePaths,
  ) async {
    try {
      await _api.put(
        '/prescriptions/update/$prescriptionId',
        {
          "note": note,
          "storagePaths": storagePaths,
        },
        requiresAuth: true,
      );
    } catch (e) {
      print('Error: $e');
      throw e;
    }
  }

  Future<void> deletePrescription(String prescriptionId) async {
    await _api.delete(
      "/prescriptions/delete/$prescriptionId",
      requiresAuth: true,
    );
  }
}
