import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';

import '../bloc/prescription_bloc.dart';
import '../bloc/prescription_event.dart';
import '../bloc/prescription_state.dart';

class PrescriptionDetailsScreen extends StatefulWidget {
  final String prescriptionId;
  const PrescriptionDetailsScreen({super.key, required this.prescriptionId});

  @override
  State<PrescriptionDetailsScreen> createState() =>
      _PrescriptionDetailsScreenState();
}

class _PrescriptionDetailsScreenState extends State<PrescriptionDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, profileState) {
        final profilePhoto =
            profileState is ProfileLoaded ? profileState.profilePhoto : null;
        final firstName =
            profileState is ProfileLoaded ? profileState.firstName : '';
        final lastName =
            profileState is ProfileLoaded ? profileState.lastName : '';

        return BlocBuilder<NotificationBloc, NotificationState>(
          builder: (context, notificationState) {
            int unreadCount = 0;
            if (notificationState is NotificationLoaded) {
              unreadCount = notificationState.unreadCount;
            } else if (notificationState is UnreadCountLoaded) {
              unreadCount = notificationState.count;
            }

            return Scaffold(
              backgroundColor: Colors.white,
              appBar: CustomAppBar(
                logoPath: 'assets/logo.png',
                notificationCount: unreadCount > 0 ? unreadCount : null,
                onNotificationTap: () {
                  Navigator.pushNamed(context, '/notifications');
                },
                onProfileTap: () =>
                    Navigator.pushReplacementNamed(context, '/profile'),
                profilePhoto: profilePhoto,
                firstName: firstName,
                lastName: lastName,
              ),
              bottomNavigationBar: CustomNavBar(
                selectedIndex: 0, // ✅ Previously 2
                onTap: (index) {
                  if (index == 0) {
                    Navigator.pushReplacementNamed(context, '/home');
                  } else if (index == 1) {
                    Navigator.pushReplacementNamed(context, '/packages');
                  } else if (index == 2) {
                    Navigator.pushReplacementNamed(context, '/shop');
                  } else if (index == 3) {
                    Navigator.pushReplacementNamed(context, '/profile');
                  }
                },
              ),
              body: BlocBuilder<PrescriptionBloc, PrescriptionState>(
                builder: (context, state) {
                  if (state is PrescriptionDetailsLoaded) {
                    final prescription = state.prescription;

                    final note =
                        prescription['note'] ?? 'Aucune description disponible';
                    final storagePaths =
                        List<String>.from(prescription['storagePath']);
                    final uploadedAt = prescription['uploadedAt'] ?? 'Inconnu';
                    final prescriptionStatus =
                        prescription['prescriptionStatus']?.toString() ??
                            'Statut non disponible';

                    String formattedUploadedAt = '';
                    try {
                      final date = DateTime.parse(uploadedAt);
                      formattedUploadedAt =
                          DateFormat("d MMMM yyyy 'à' HH:mm", 'fr_FR')
                              .format(date);
                    } catch (_) {
                      formattedUploadedAt = 'Date inconnue';
                    }

                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.arrow_back,
                                    color: Colors.black),
                                onPressed: () => Navigator.pop(context),
                              ),
                              const Expanded(
                                child: Center(
                                  child: Text(
                                    "Détails de l'ordonnance",
                                    style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 40),
                            ],
                          ),
                          const SizedBox(height: 16),

                          const Text('Description:',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          Text(note),
                          const SizedBox(height: 16),

                          const Text('Ajoutée le:',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          Text(formattedUploadedAt),
                          const SizedBox(height: 16),

                          const Text('Statut de l’ordonnance:',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          Text(prescriptionStatus),
                          const SizedBox(height: 16),

                          if (storagePaths.isNotEmpty)
                            const Text('Documents:',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),

                          if (storagePaths.isNotEmpty)
                            Stack(
                              children: [
                                SizedBox(
                                  height: 150,
                                  child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    itemCount: storagePaths.length,
                                    itemBuilder: (context, index) {
                                      final path = storagePaths[index];
                                      final isPdf =
                                          path.toLowerCase().endsWith('.pdf');
                                      final fullUrl =
                                          'http://172.20.10.4:3000$path';

                                      return Padding(
                                        padding:
                                            const EdgeInsets.only(right: 8),
                                        child: GestureDetector(
                                          onTap: () => isPdf
                                              ? _openPdf(fullUrl)
                                              : _showImage(fullUrl),
                                          child: isPdf
                                              ? Container(
                                                  width: 100,
                                                  height: 120,
                                                  decoration: BoxDecoration(
                                                    color: Colors.red.shade50,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                    boxShadow: [
                                                      BoxShadow(
                                                        color: Colors.black12,
                                                        blurRadius: 2,
                                                        offset: Offset(1, 2),
                                                      ),
                                                    ],
                                                  ),
                                                  child: const Center(
                                                    child: Icon(
                                                      Icons.picture_as_pdf,
                                                      color: Colors.red,
                                                      size: 40,
                                                    ),
                                                  ),
                                                )
                                              : ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                  child: Image.network(
                                                    fullUrl,
                                                    width: 120,
                                                    height: 120,
                                                    fit: BoxFit.cover,
                                                    errorBuilder: (_, __,
                                                            ___) =>
                                                        const Icon(
                                                            Icons.broken_image,
                                                            size: 80),
                                                  ),
                                                ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                // Badge for count
                                Positioned(
                                  top: 0,
                                  right: 0,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 6, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: Colors.blue,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      '${storagePaths.length}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    );
                  } else if (state is PrescriptionError) {
                    return Center(child: Text('Erreur : ${state.message}'));
                  }
                  return const Center(child: CircularProgressIndicator());
                },
              ),
            );
          },
        );
      },
    );
  }

  void _showImage(String imageUrl) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: InteractiveViewer(
            child: Image.network(imageUrl),
          ),
        );
      },
    );
  }

  void _openPdf(String pdfUrl) async {
    try {
      final response = await http.get(Uri.parse(pdfUrl));
      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;
        final tempDir = await getTemporaryDirectory();
        final file = File('${tempDir.path}/${pdfUrl.split('/').last}');
        await file.writeAsBytes(bytes);
        await OpenFile.open(file.path);
      } else {
        throw Exception("Download failed");
      }
    } catch (_) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Impossible d’ouvrir le PDF.')),
      );
    }
  }
}
