import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:open_file/open_file.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_app_bar.dart';
import 'package:patiant_app/features/authentication/presentation/widgets/custom_nav_bar.dart';
import 'package:patiant_app/features/prescription/presentation/bloc/prescription_state.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:patiant_app/features/profile/presentation/bloc/profile_state.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_bloc.dart';
import 'package:patiant_app/features/notifications/presentation/bloc/notification_state.dart';
import '../../data/repositories/prescription_repository.dart';
import '../bloc/prescription_bloc.dart';
import '../bloc/prescription_event.dart';

class CreatePrescriptionScreen extends StatefulWidget {
  @override
  _CreatePrescriptionScreenState createState() =>
      _CreatePrescriptionScreenState();
}

class _CreatePrescriptionScreenState extends State<CreatePrescriptionScreen> {
  List<File> files = [];
  String? note;
  bool _isLoading = false;

  void pickFiles() async {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      allowedExtensions: ['pdf'],
      type: FileType.custom,
    );
    if (result != null) {
      setState(() {
        files.addAll(result.paths.map((e) => File(e!)));
      });
    }
  }

  void pickFromGallery() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      allowMultiple: true,
    );

    if (result != null) {
      setState(() {
        files.addAll(result.paths.map((path) => File(path!)));
      });
    }
  }

  void takePhoto() async {
    final pickedFile =
        await ImagePicker().pickImage(source: ImageSource.camera);
    if (pickedFile != null) {
      setState(() => files.add(File(pickedFile.path)));
    }
  }

  void removeFile(int index) {
    setState(() => files.removeAt(index));
  }

  Future<void> submit() async {
    if (files.isEmpty || _isLoading) return;
    setState(() => _isLoading = true);

    try {
      final repo = context.read<PrescriptionRepository>();
      final paths = await repo.uploadFiles(files);
      context
          .read<PrescriptionBloc>()
          .add(CreatePrescription(paths, note: note));
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text("Erreur lors de la sauvegarde."),
      ));
    }
  }

  Widget _buildFilePreview(File file, int index) {
    final isPdf = file.path.toLowerCase().endsWith('.pdf');
    if (isPdf) {
      return ListTile(
        leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
        title: Text(file.path.split('/').last),
        trailing: IconButton(
          icon: const Icon(Icons.delete, color: Colors.grey),
          onPressed: () => removeFile(index),
        ),
        onTap: () => OpenFile.open(file.path),
      );
    } else {
      return Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              file,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            top: 2,
            right: 2,
            child: GestureDetector(
              onTap: () => removeFile(index),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
                padding: const EdgeInsets.all(4),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final imageFiles = files
        .asMap()
        .entries
        .where((e) => !e.value.path.toLowerCase().endsWith('.pdf'))
        .toList();
    final pdfFiles = files
        .asMap()
        .entries
        .where((e) => e.value.path.toLowerCase().endsWith('.pdf'))
        .toList();

    return BlocListener<PrescriptionBloc, PrescriptionState>(
      listener: (context, state) {
        if (state is PrescriptionSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Prescription créée avec succès!')),
          );
          Navigator.pop(context, true);
        }

        if (state is PrescriptionError) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, profileState) {
          final profilePhoto =
              profileState is ProfileLoaded ? profileState.profilePhoto : null;
          final firstName =
              profileState is ProfileLoaded ? profileState.firstName : '';
          final lastName =
              profileState is ProfileLoaded ? profileState.lastName : '';

          return BlocBuilder<NotificationBloc, NotificationState>(
            builder: (context, notificationState) {
              int unreadCount = 0;
              if (notificationState is NotificationLoaded) {
                unreadCount = notificationState.unreadCount;
              } else if (notificationState is UnreadCountLoaded) {
                unreadCount = notificationState.count;
              }

              return Scaffold(
                backgroundColor: Colors.white,
                appBar: CustomAppBar(
                  logoPath: 'assets/logo.png',
                  notificationCount: unreadCount > 0 ? unreadCount : null,
                  onNotificationTap: () {
                    Navigator.pushNamed(context, '/notifications');
                  },
                  onProfileTap: () =>
                      Navigator.pushReplacementNamed(context, '/profile'),
                  profilePhoto: profilePhoto,
                  firstName: firstName,
                  lastName: lastName,
                ),
                bottomNavigationBar: CustomNavBar(
                  selectedIndex: 0, // ✅ Previously 2
                  onTap: (index) {
                    if (index == 0) {
                      Navigator.pushReplacementNamed(context, '/home');
                    } else if (index == 1) {
                      Navigator.pushReplacementNamed(context, '/packages');
                    } else if (index == 2) {
                      Navigator.pushReplacementNamed(context, '/shop');
                    } else if (index == 3) {
                      Navigator.pushReplacementNamed(context, '/profile');
                    }
                  },
                ),
                body: SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Custom Back Button with Title
                      Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.arrow_back,
                                color: Colors.black),
                            onPressed: () => Navigator.pop(context),
                          ),
                          const Expanded(
                            child: Center(
                              child: Text(
                                "Nouvelle ordonnance",
                                style: TextStyle(
                                    fontSize: 20, fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                          const SizedBox(
                              width: 40), // to balance back icon space
                        ],
                      ),
                      const SizedBox(height: 16),

                      TextField(
                        decoration:
                            const InputDecoration(labelText: 'Description'),
                        onChanged: (val) => note = val,
                      ),
                      const SizedBox(height: 16),
                      Wrap(
                        spacing: 10,
                        runSpacing: 10,
                        children: [
                          ElevatedButton.icon(
                            onPressed: pickFiles,
                            icon: const Icon(Icons.picture_as_pdf),
                            label: const Text('PDF'),
                          ),
                          ElevatedButton.icon(
                            onPressed: pickFromGallery,
                            icon: const Icon(Icons.photo_library),
                            label: const Text('Galerie'),
                          ),
                          ElevatedButton.icon(
                            onPressed: takePhoto,
                            icon: const Icon(Icons.camera_alt),
                            label: const Text('Caméra'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      if (imageFiles.isNotEmpty)
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: imageFiles.length,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            mainAxisSpacing: 8,
                            crossAxisSpacing: 8,
                            childAspectRatio: 1,
                          ),
                          itemBuilder: (context, i) => _buildFilePreview(
                              imageFiles[i].value, imageFiles[i].key),
                        ),
                      if (pdfFiles.isNotEmpty)
                        ...pdfFiles.map(
                          (e) => _buildFilePreview(e.value, e.key),
                        ),
                      const SizedBox(height: 20),
                      ElevatedButton.icon(
                        onPressed:
                            files.isNotEmpty && !_isLoading ? submit : null,
                        icon: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.save),
                        label:
                            Text(_isLoading ? 'Chargement...' : 'Sauvegarder'),
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size(double.infinity, 50),
                        ),
                      )
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
